-- 生产异常标记表
CREATE TABLE `pro_task_exception` (
  `exception_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '异常记录ID',
  `task_id` bigint(20) NOT NULL COMMENT '生产任务ID',
  `task_code` varchar(64) DEFAULT NULL COMMENT '生产任务编号',
  `workorder_id` bigint(20) DEFAULT NULL COMMENT '生产工单ID',
  `workorder_code` varchar(64) DEFAULT NULL COMMENT '生产工单编号',
  `item_code` varchar(64) NOT NULL COMMENT '原材料代码',
  `item_name` varchar(255) DEFAULT NULL COMMENT '原材料名称',
  `exception_reason` varchar(64) NOT NULL COMMENT '异常原因（字典值）',
  `exception_quantity` decimal(12,4) NOT NULL COMMENT '异常数量',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `registrant` varchar(64) DEFAULT NULL COMMENT '登记人',
  `registrant_name` varchar(64) DEFAULT NULL COMMENT '登记人姓名',
  `register_time` datetime DEFAULT NULL COMMENT '登记时间',
  `attachment_url` varchar(500) DEFAULT NULL COMMENT '附件URL',
  `attachment_name` varchar(255) DEFAULT NULL COMMENT '附件名称',
  `attachment_size` bigint(20) DEFAULT NULL COMMENT '附件大小（字节）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark_sys` varchar(500) DEFAULT '' COMMENT '系统备注',
  PRIMARY KEY (`exception_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_workorder_id` (`workorder_id`),
  KEY `idx_item_code` (`item_code`),
  KEY `idx_exception_reason` (`exception_reason`),
  KEY `idx_register_time` (`register_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产异常标记表';

-- 异常原因字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) 
VALUES ('生产异常原因', 'mes_production_exception_reason', '0', 'admin', NOW(), '生产过程中的异常原因分类');

-- 异常原因字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '来料破损', 'llps', 'mes_production_exception_reason', '', 'default', 'N', '0', 'admin', NOW(), '原材料在运输或存储过程中发生破损'),
(2, '生产异常', 'scyc', 'mes_production_exception_reason', '', 'default', 'N', '0', 'admin', NOW(), '生产过程中发生的异常情况'),
(3, '设备故障', 'sbgz', 'mes_production_exception_reason', '', 'default', 'N', '0', 'admin', NOW(), '生产设备发生故障导致的异常'),
(4, '人为操作失误', 'rwczss', 'mes_production_exception_reason', '', 'default', 'N', '0', 'admin', NOW(), '操作人员失误导致的异常'),
(5, '质量不合格', 'zlbhg', 'mes_production_exception_reason', '', 'default', 'N', '0', 'admin', NOW(), '原材料或半成品质量不符合要求'),
(6, '其他异常', 'qtyc', 'mes_production_exception_reason', '', 'default', 'N', '0', 'admin', NOW(), '其他未分类的异常情况');
