<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ktg</artifactId>
        <groupId>com.ktg</groupId>
        <version>3.8.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ktg-mes</artifactId>

    <description>
        MES 系统
    </description>
    <dependencies>
        <!--jasperreport组件-->
        <!--        <dependency>-->
        <!--            <groupId>net.sf.jasperreports</groupId>-->
        <!--            <artifactId>jasperreports</artifactId>-->
        <!--            <version>6.18.1</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>net.sf.jasperreports</groupId>-->
        <!--            <artifactId>jasperreports-fonts</artifactId>-->
        <!--            <version>6.18.1</version>-->
        <!--        </dependency>-->
        <!--PDF生成组件-->
        <!--        <dependency>-->
        <!--            <groupId>com.itextpdf</groupId>-->
        <!--            <artifactId>itextpdf</artifactId>-->
        <!--            <version>5.5.11</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.itextpdf</groupId>-->
        <!--            <artifactId>itext-pdfa</artifactId>-->
        <!--            <version>5.5.11</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.itextpdf</groupId>-->
        <!--            <artifactId>itext-asian</artifactId>-->
        <!--            <version>5.2.0</version>-->
        <!--        </dependency>-->
        <!-- 通用工具-->
        <dependency>
            <groupId>com.ktg</groupId>
            <artifactId>ktg-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.6.2</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>dubbo-component</artifactId>
        </dependency>
        <!--webSocket-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
            <version>2.2.13.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>wms-mes-rpc-client</artifactId>
            <version>3.2.12-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-goods-rpc-client</artifactId>
            <version>3.2.3-1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

</project>
