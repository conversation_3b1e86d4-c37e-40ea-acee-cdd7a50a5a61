<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProWorkorderBomMapper">
    <resultMap type="ProWorkorderBom" id="ProWorkorderBomResult">
        <result property="lineId"    column="line_id"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="itemSpc"    column="item_spc"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="itemOrProduct"    column="item_or_product"    />
        <result property="quantity"    column="quantity"    />
        <result property="useRatio"    column="use_ratio"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProWorkorderBomVo">
        select line_id, workorder_id, item_id, item_code, item_name, item_spc, unit_of_measure, item_or_product, quantity, use_ratio, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from pro_workorder_bom
    </sql>

    <select id="selectProWorkorderBomList" parameterType="ProWorkorderBom" resultMap="ProWorkorderBomResult">
        <include refid="selectProWorkorderBomVo"/>
        <where>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="itemSpc != null  and itemSpc != ''"> and item_spc = #{itemSpc}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="itemOrProduct != null  and itemOrProduct != ''"> and item_or_product = #{itemOrProduct}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="useRatio != null "> and use_ratio = #{useRatio}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
        </where>
    </select>

    <select id="selectProWorkorderBomByLineId" parameterType="Long" resultMap="ProWorkorderBomResult">
        <include refid="selectProWorkorderBomVo"/>
        where line_id = #{lineId}
    </select>

    <insert id="insertProWorkorderBom" parameterType="ProWorkorderBom" useGeneratedKeys="true" keyProperty="lineId">
        insert into pro_workorder_bom
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workorderId != null">workorder_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="itemSpc != null">item_spc,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="itemOrProduct != null and itemOrProduct != ''">item_or_product,</if>
            <if test="quantity != null">quantity,</if>
            <if test="useRatio != null">use_ratio,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workorderId != null">#{workorderId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="itemSpc != null">#{itemSpc},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="itemOrProduct != null and itemOrProduct != ''">#{itemOrProduct},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="useRatio != null">#{useRatio},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateProWorkorderBom" parameterType="ProWorkorderBom">
        update pro_workorder_bom
        <trim prefix="SET" suffixOverrides=",">
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="itemSpc != null">item_spc = #{itemSpc},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="itemOrProduct != null and itemOrProduct != ''">item_or_product = #{itemOrProduct},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="useRatio != null">use_ratio = #{useRatio},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where line_id = #{lineId}
    </update>

    <delete id="deleteProWorkorderBomByLineId" parameterType="Long">
        delete from pro_workorder_bom where line_id = #{lineId}
    </delete>

    <delete id="deleteProWorkorderBomByLineIds" parameterType="String">
        delete from pro_workorder_bom where line_id in
        <foreach item="lineId" collection="array" open="(" separator="," close=")">
            #{lineId}
        </foreach>
    </delete>

    <delete id="deleteProWorkorderBomByWorkorderId" parameterType="Long">
        delete from pro_workorder_bom where workorder_id = #{workorderId}
    </delete>

    <select id="selectProWorkorderBomListByWorkorderIdList" resultMap="ProWorkorderBomResult">
        <include refid="selectProWorkorderBomVo"/>
        <where>
            <if test="workorderIdList != null and workorderIdList.size > 0">
                workorder_id in
                <foreach collection="workorderIdList" item="workorderId" open="(" close=")" separator=",">
                    #{workorderId}
                </foreach>
            </if>
        </where>
        order by workorder_id, line_id
    </select>
</mapper>