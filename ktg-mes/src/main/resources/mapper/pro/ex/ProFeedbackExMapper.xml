<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProFeedbackMapper">

    <select id="proFeedbackPage" parameterType="ProFeedbackListReqVo" resultMap="ProFeedbackResult">
        <include refid="selectProFeedbackVo"/>
        <where>
            <if test="feedbackCode != null  and feedbackCode != ''"> and feedback_code like concat('%', #{feedbackCode}, '%')</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code like concat('%', #{workorderCode}, '%')</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code  like concat('%', #{taskCode}, '%')</if>
            <if test="feedbackTimeBegin != null and feedbackTimeEnd != null"> and feedback_time between #{feedbackTimeBegin} and #{feedbackTimeEnd}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code like concat('%', #{itemCode}, '%')</if>
            <if test="wmsOrderNo != null  and wmsOrderNo != ''"> and wms_order_no like concat('%', #{wmsOrderNo}, '%')</if>
            <if test="workstationId != null and workstationId != ''"> and workstation_id = #{workstationId}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectProFeedbackByWorkorderIdList" resultMap="ProFeedbackResult">
        <include refid="selectProFeedbackVo"/>
        where workorder_id in
        <foreach item="workorderId" collection="workorderIdList" open="(" separator="," close=")">
            #{workorderId}
        </foreach>
    </select>

    <select id="selectProFeedbackByTaskIdList" resultMap="ProFeedbackResult">
        <include refid="selectProFeedbackVo"/>
        where task_id in
        <foreach item="taskId" collection="taskIdList" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>

    <select id="selectProFeedbackBySkuLotNoList" resultMap="ProFeedbackResult">
        <include refid="selectProFeedbackVo"/>
        <where>
            <if test="skuLotNoList != null and skuLotNoList.size > 0">
                <foreach collection="skuLotNoList" item="skuLotNo" separator=" OR " open="(" close=")">
                    JSON_SEARCH(wms_batch_detail, 'one', #{skuLotNo}, NULL, '$[*].skuLotNo') IS NOT NULL
                </foreach>
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>