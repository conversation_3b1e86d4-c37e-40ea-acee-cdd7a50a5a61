<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmIssueLineMapper">

    <select id="selectWmIssueLineByIssueIdList" parameterType="Long" resultMap="WmIssueLineResult">
        <include refid="selectWmIssueLineVo"/>
        where issue_id in
        <foreach item="lineId" collection="issueIdList" open="(" separator="," close=")">
            #{lineId}
        </foreach>
    </select>

    <select id="selectWmIssueLineBySkuAndSkuLotNoList" resultMap="WmIssueLineResult">
        <include refid="selectWmIssueLineVo"/>
        where item_code = #{sku} and sku_lot_no in
        <foreach item="skuLotNo" collection="skuLotNoList" open="(" separator="," close=")">
            #{skuLotNo}
        </foreach>
    </select>

    <select id="selectWmissueLineByWorkorderList" resultMap="WmIssueLineResult">
        <include refid="selectWmIssueLineVo"/>
        where issue_id in (
            select issue_id from wm_issue_header
            where workorder_id in
            <foreach collection="workorderIdList" item="workorderId" open="(" close=")" separator=",">
                #{workorderId}
            </foreach>
        )
        order by issue_id, line_id
    </select>


</mapper>