package com.ktg.mes.wm.controller;

import com.alibaba.fastjson.JSON;
import com.ktg.common.annotation.Log;
import com.ktg.common.constant.UserConstants;
import com.ktg.common.core.controller.BaseController;
import com.ktg.common.core.domain.AjaxResult;
import com.ktg.common.core.page.TableDataInfo;
import com.ktg.common.enums.BusinessType;
import com.ktg.common.enums.TakeStockStatusEnum;
import com.ktg.common.utils.ConvertUtil;
import com.ktg.common.utils.StringUtils;
import com.ktg.common.utils.UserThreadLocalUtil;
import com.ktg.common.utils.poi.ExcelUtil;
import com.ktg.mes.qc.domain.QcIqc;
import com.ktg.mes.qc.service.IQcIqcService;
import com.ktg.mes.qc.service.IQcTemplateService;
import com.ktg.mes.wm.domain.WmArrivalNotice;
import com.ktg.mes.wm.domain.WmArrivalNoticeLine;
import com.ktg.mes.wm.domain.req.WmArrivalNoticeLeftSubmitReqVo;
import com.ktg.mes.wm.domain.req.WmArrivalNoticeListReqVo;
import com.ktg.mes.wm.domain.res.*;
import com.ktg.mes.wm.service.IWmArrivalNoticeLineService;
import com.ktg.mes.wm.service.IWmArrivalNoticeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * WarehouseManage 到货通知单
 *
 * <AUTHOR>
 * @menu 到货通知单
 * @date 2024-11-12
 */
@Slf4j
@RestController
@RequestMapping("/mes/wm/arrivalnotice")
public class WmArrivalNoticeController extends BaseController {
    @Autowired
    private IWmArrivalNoticeService wmArrivalNoticeService;

    @Autowired
    private IWmArrivalNoticeLineService wmArrivalNoticeLineService;

    @Autowired
    private IQcTemplateService qcTemplateService;

    @Autowired
    private IQcIqcService qcIqcService;

    /**
     * 查询到货通知单列表
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmArrivalNoticeListReqVo reqVo) {
        startPage();
        List<WmArrivalNotice> list = wmArrivalNoticeService.wmArrivalNoticePage(reqVo);
        TableDataInfo dataTable = getDataTable(list);
        List<WmArrivalNoticeListResVo> resVoList = list.stream().map(l -> {
            WmArrivalNoticeListResVo wmArrivalNoticeListResVo = ConvertUtil.beanConvert(l, WmArrivalNoticeListResVo.class);
            if (Objects.nonNull(l.getTakeStockStatus())) {
                TakeStockStatusEnum stockStatusEnum = TakeStockStatusEnum.getByCode(l.getTakeStockStatus());
                if (Objects.nonNull(stockStatusEnum)) {
                    wmArrivalNoticeListResVo.setTakeStockStatusDesc(stockStatusEnum.getInfo());
                }
            }
            return wmArrivalNoticeListResVo;
        }).collect(Collectors.toList());
        dataTable.setRows(resVoList);
        return dataTable;
    }

    /**
     * 导出到货通知单列表
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:export')")
    @Log(title = "到货通知单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmArrivalNotice wmArrivalNotice) {
        List<WmArrivalNotice> list = wmArrivalNoticeService.selectWmArrivalNoticeList(wmArrivalNotice);
        ExcelUtil<WmArrivalNotice> util = new ExcelUtil<WmArrivalNotice>(WmArrivalNotice.class);
        util.exportExcel(response, list, "到货通知单数据");
    }

    /**
     * 获取到货通知单详细信息
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:query')")
    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable("noticeId") Long noticeId) {
        WmArrivalNotice wmArrivalNotice = wmArrivalNoticeService.selectWmArrivalNoticeByNoticeId(noticeId);
        List<WmArrivalNoticeLine> wmArrivalNoticeLines = wmArrivalNoticeLineService.selectWmArrivalNoticeLineByNoticeId(wmArrivalNotice.getNoticeId());
        WmArrivalNoticeInfoResVo wmArrivalNoticeInfoResVo = new WmArrivalNoticeInfoResVo();
        WmArrivalNoticeDetailResVo detailResVo = ConvertUtil.beanConvert(wmArrivalNotice, WmArrivalNoticeDetailResVo.class);
        wmArrivalNoticeInfoResVo.setNotice(detailResVo);
        List<Long> iqcIdList = wmArrivalNoticeLines.stream().map(WmArrivalNoticeLine::getIqcId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<QcIqc> iqcList = qcIqcService.selectQcIqcByIqcId(iqcIdList);
        Map<Long, QcIqc> qcIqcMap;
        if (!CollectionUtils.isEmpty(iqcList)) {
            qcIqcMap = iqcList.stream().collect(Collectors.toMap(QcIqc::getIqcId, Function.identity(), (v1, v2) -> v1));
        } else {
            qcIqcMap = new HashMap<>();
        }
        List<WmArrivalNoticeLineResVo> lineResVoList = wmArrivalNoticeLines.stream().map(line -> {
            WmArrivalNoticeLineResVo wmArrivalNoticeLineResVo = ConvertUtil.beanConvert(line, WmArrivalNoticeLineResVo.class);
            if (Objects.equals(line.getIqcCheck(), "Y")){
                wmArrivalNoticeLineResVo.setIqcCheckDesc("是");
            } else if (Objects.equals(line.getIqcCheck(),"N")) {
                wmArrivalNoticeLineResVo.setIqcCheckDesc("否");
            }
            if (Objects.nonNull(line.getIqcId())) {
                if (qcIqcMap.containsKey(line.getIqcId())) {
                    QcIqc qcIqc = qcIqcMap.get(line.getIqcId());
                    if (Objects.nonNull(qcIqc)) {
                        wmArrivalNoticeLineResVo.setIqcId(qcIqc.getIqcId());
                        wmArrivalNoticeLineResVo.setIqcCode(qcIqc.getIqcCode());
                        wmArrivalNoticeLineResVo.setIqcStatus(qcIqc.getStatus());
                        if (!StringUtils.isEmpty(qcIqc.getCheckResult())){
                            if (Objects.equals(qcIqc.getCheckResult(),"ACCEPT")){
                                wmArrivalNoticeLineResVo.setIqcResult("检测通过");
                            }else if (Objects.equals(qcIqc.getCheckResult(),"REJECT")){
                                wmArrivalNoticeLineResVo.setIqcResult("检测不通过");
                            }
                        }
                    }
                }
            }
            return wmArrivalNoticeLineResVo;
        }).collect(Collectors.toList());
        wmArrivalNoticeInfoResVo.setLineList(lineResVoList);
        return AjaxResult.success(wmArrivalNoticeInfoResVo);
    }

    /**
     * 新增到货通知单
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:add')")
    @Log(title = "到货通知单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmArrivalNotice wmArrivalNotice) {
        if (UserConstants.NOT_UNIQUE.equals(wmArrivalNoticeService.checkRnCodeUnique(wmArrivalNotice))) {
            return AjaxResult.error("单据编号已存在");
        }

        return toAjax(wmArrivalNoticeService.insertWmArrivalNotice(wmArrivalNotice));
    }

    /**
     * 修改到货通知单
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:edit')")
    @Log(title = "到货通知单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmArrivalNotice wmArrivalNotice) {
        if (UserConstants.NOT_UNIQUE.equals(wmArrivalNoticeService.checkRnCodeUnique(wmArrivalNotice))) {
            return AjaxResult.error("单据编号已存在");
        }

        //提交时判断通知单行上的物料
        if (UserConstants.ORDER_STATUS_APPROVING.equals(wmArrivalNotice.getStatus())) {
            //到货内容检查
            WmArrivalNoticeLine param = new WmArrivalNoticeLine();
            param.setNoticeId(wmArrivalNotice.getNoticeId());
            List<WmArrivalNoticeLine> lines = wmArrivalNoticeLineService.selectWmArrivalNoticeLineList(param);
            if (CollectionUtils.isEmpty(lines)) {
                return AjaxResult.error("请添加到货物资！");
            }
        }
        return toAjax(wmArrivalNoticeService.updateWmArrivalNotice(wmArrivalNotice));
    }

    /**
     * 删除到货通知单
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:remove')")
    @Log(title = "到货通知单", businessType = BusinessType.DELETE)
    @Transactional
    @DeleteMapping("/{noticeIds}")
    public AjaxResult remove(@PathVariable Long[] noticeIds) {
        for (Long noticeId : noticeIds) {

            WmArrivalNotice notice = wmArrivalNoticeService.selectWmArrivalNoticeByNoticeId(noticeId);
            if (StringUtils.isNotNull(notice) && !UserConstants.ORDER_STATUS_PREPARE.equals(notice.getStatus())) {
                return AjaxResult.error("只能删除草稿状态的单据!");
            }
            wmArrivalNoticeLineService.deleteByNoticeId(noticeId);
        }

        return toAjax(wmArrivalNoticeService.deleteWmArrivalNoticeByNoticeIds(noticeIds));
    }

    /**
     * 实收数量
     *
     * @param noticeId
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:query')")
    @GetMapping("/receiveInfo/{noticeId}")
    public AjaxResult receiveInfo(@PathVariable("noticeId") Long noticeId) {
        try {
            List<WmArriveNoticeInfoResVo> infoResVos = wmArrivalNoticeService.receiveInfo(noticeId);
            return AjaxResult.success(infoResVos);
        } catch (Exception e) {
            log.error("查看到货数量失败," + e.getMessage(), e);
            return AjaxResult.error("查看到货数量失败," + e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:query')")
    @GetMapping("/receiveInfo/detail/{noticeId}")
    public AjaxResult receiveInfoDetail(@PathVariable("noticeId") Long noticeId) {
        try {
            List<WmArrivalNoticeLine> wmArrivalNoticeLines = wmArrivalNoticeLineService.selectWmArrivalNoticeLineByNoticeId(noticeId);
            List<WmArrivalNoticeLineResVo> resVoList = ConvertUtil.listConvert(wmArrivalNoticeLines, WmArrivalNoticeLineResVo.class);
            return AjaxResult.success(resVoList);
        }catch (Exception e){
            log.error("获取到货通知单详情失败:{}",e.getMessage(),e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 查询余料清点库存接口
     * @param noticeId
     * @return
     */
    @GetMapping("/leftView/{noticeId}")
    public AjaxResult leftView(@PathVariable("noticeId") Long noticeId) {
        log.info("leftView noticeId={}", noticeId);
        try {
//            WmArrivalNoticeLeftViewResVo resVo = wmArrivalNoticeService.leftView(noticeId);
            WmArrivalNoticeLeftViewNewResVo resVo = wmArrivalNoticeService.leftViewNew(noticeId);
            return AjaxResult.success(resVo);
        } catch (Exception e) {
            log.error("查询失败:{}", e.getMessage(), e);
            return AjaxResult.error("查询失败:"+e.getMessage());
        }
    }

    /**
     * 余料清点-提交请求
     * @param reqVo
     * @return
     */
    @Deprecated
    @PostMapping("/leftSubmit")
    public AjaxResult leftSubmit(@RequestBody WmArrivalNoticeLeftSubmitReqVo reqVo) {
        log.info("leftSubmit reqVo={}", JSON.toJSONString(reqVo));
        try {
            reqVo.setOperator(getUsername());
            wmArrivalNoticeService.leftSubmit(reqVo);
            return AjaxResult.success("余料清点成功");
        } catch (Exception e) {
            log.error("查询失败:{}", e.getMessage(), e);
            return AjaxResult.error("余料清点失败:"+e.getMessage());
        }
    }

    /**
     * 发起余料清点
     * @param lineId
     * @return
     */
    @GetMapping("/leftSubmitStart/{lineId}")
    public AjaxResult leftSubmitStart(@PathVariable("lineId") Long lineId) {
        log.info("leftSubmitStart lineId={}", lineId);
        try {
            String username = getUsername();
            UserThreadLocalUtil.setUsername(username);
            wmArrivalNoticeService.leftSubmitStart(lineId);
            return AjaxResult.success("发起余料清单成功！请联系库管及时完成盘点单");
        }catch (Exception e){
            log.error("发起余料清点失败:{}",e.getMessage(),e);
            return AjaxResult.error("发起余料清点失败");
        }
    }


    /**
     * 完成余料清点
     * @param lineId
     * @return
     */
    @GetMapping("/leftSubmitFinish/{lineId}")
    public AjaxResult leftSubmitFinish(@PathVariable("lineId") Long lineId) {
        log.info("leftSubmitFinish lineId={}", lineId);
        try {
            String username = getUsername();
            UserThreadLocalUtil.setUsername(username);
            wmArrivalNoticeService.leftSubmitFinish(lineId);
            return AjaxResult.success("余料清点已完成！余料数量已更新");
        }catch (Exception e){
            log.error("完成余料清点失败:{}",e.getMessage(),e);
            return AjaxResult.error("完成余料清点失败");
        }
    }


    /**
     * 点击余料清点状态，查看明细接口
     * @param noticeId
     * @return
     */
    @GetMapping("/leftItemProcess/{noticeId}")
    public AjaxResult leftItemProcess(@PathVariable("noticeId") Long noticeId) {
        log.info("leftItemProcess noticeId={}", noticeId);
        try {
//            WmArrivalNoticeLeftInfoResVo resVo = wmArrivalNoticeService.leftItemProcess(noticeId);
            WmArrivalNoticeLeftInfoNewResVo resVo = wmArrivalNoticeService.leftItemProcessNew(noticeId);
            return AjaxResult.success(resVo);
        }catch (Exception e){
            log.error("查询失败:{}",e.getMessage(),e);
            return AjaxResult.error("查询失败:"+e.getMessage());
        }

    }



}
