package com.ktg.mes.pro.domain.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 生产工单新增请求参数
 * @date 2025/4/17 13:45
 */
@Data
public class ProWorkOrderAddReqVo implements Serializable {

    /**
     * 工单数量
     */
    private BigDecimal quantity;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 产品物料id
     */
    private Long productId;

    /**
     * 产品物料编码
     */
    private String productCode;

    /**
     * 产品物料名称
     */
    private String productName;

    /**
     * 车间id
     */
    private Long workshopId;

    /**
     * 车间名称
     */
    private String workshopName;

    /**
     * 出货检验
     */
    private String oqcCheck;

    /**
     * 需求日期
     */
    private Date requestDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 关键原材料要求
     */
    private List<ProWorkOrderKeyMdItemReqVo> keyItems;

    /**
     * 生产需求
     */
    private String requirement;

    /**
     * 生产日期
     */
    private Date productionDate;

    /** 生产批次号 */
    private String produceBatchCode;

}
