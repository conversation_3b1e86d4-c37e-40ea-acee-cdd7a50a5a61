package com.ktg.mes.pro.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.platform.wms.rpc.client.mes.adjust.AdjustQueryRequest;
import com.dt.platform.wms.rpc.client.mes.adjust.AdjustQueryResponse;
import com.dt.platform.wms.rpc.client.mes.adjust.IAdjustMesQuery;
import com.dt.platform.wms.rpc.client.mes.common.Result;
import com.ktg.common.annotation.Log;
import com.ktg.common.constant.UserConstants;
import com.ktg.common.core.controller.BaseController;
import com.ktg.common.core.domain.AjaxResult;
import com.ktg.common.core.page.TableDataInfo;
import com.ktg.common.enums.BusinessType;
import com.ktg.common.enums.OrderStatusEnum;
import com.ktg.common.utils.ConvertUtil;
import com.ktg.common.utils.StringUtils;
import com.ktg.common.utils.poi.ExcelUtil;
import com.ktg.mes.md.domain.MdWorkstation;
import com.ktg.mes.md.service.IMdWorkstationService;
import com.ktg.mes.pro.domain.ProFeedback;
import com.ktg.mes.pro.domain.ProRouteProcess;
import com.ktg.mes.pro.domain.ProTask;
import com.ktg.mes.pro.domain.ProWorkorder;
import com.ktg.mes.pro.domain.req.ProFeedbackAddReqVo;
import com.ktg.mes.pro.domain.req.ProFeedbackApproveReqVo;
import com.ktg.mes.pro.domain.req.ProFeedbackEditReqVo;
import com.ktg.mes.pro.domain.req.ProFeedbackListReqVo;
import com.ktg.mes.pro.domain.res.*;
import com.ktg.mes.pro.service.IProFeedbackService;
import com.ktg.mes.pro.service.IProRouteProcessService;
import com.ktg.mes.pro.service.IProTaskService;
import com.ktg.mes.pro.service.IProWorkorderService;
import com.ktg.mes.wm.domain.WmItemConsume;
import com.ktg.mes.wm.domain.WmProductProduce;
import com.ktg.mes.wm.domain.tx.ItemConsumeTxBean;
import com.ktg.mes.wm.domain.tx.ProductProductTxBean;
import com.ktg.mes.wm.service.IStorageCoreService;
import com.ktg.mes.wm.service.IWmItemConsumeService;
import com.ktg.mes.wm.service.IWmProductProduceService;
import com.ktg.system.strategy.AutoCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 生产报工记录Controller
 *
 * <AUTHOR>
 * @menu 生产报工
 * @date 2022-07-10
 */
@Slf4j
@RestController
@RequestMapping("/mes/pro/feedback")
public class ProFeedbackController extends BaseController {
    @Autowired
    private IProFeedbackService proFeedbackService;

    @Autowired
    private IProTaskService proTaskService;

    @Autowired
    private IProRouteProcessService proRouteProcessService;

    @Autowired
    private IProWorkorderService proWorkorderService;

    @Autowired
    private IMdWorkstationService mdWorkstationService;

    @Autowired
    private IWmItemConsumeService wmItemConsumeService;

    @Autowired
    private IWmProductProduceService wmProductProduceService;

    @Autowired
    private IStorageCoreService storageCoreService;

    @Autowired
    private AutoCodeUtil autoCodeUtil;

    @DubboReference
    private IAdjustMesQuery iAdjustMesQuery;

    /**
     * 查询生产报工记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ProFeedbackListReqVo reqVo) {
        startPage();
        List<ProFeedback> list = proFeedbackService.proFeedbackPage(reqVo);
        TableDataInfo dataTable = getDataTable(list);
        Map<String, String> orderStatusMap = new HashMap<>(32);
        Map<String, List<ProFeedback>> warehouseRecptMap = list.stream().filter(feedback->Objects.nonNull(feedback.getWmsOrderNo()))
                .collect(Collectors.groupingBy(ProFeedback::getWarehouseCode));
        warehouseRecptMap.forEach((code, issueHeaders) -> {
            AdjustQueryRequest adjustQueryRequest = new AdjustQueryRequest();
            adjustQueryRequest.setWarehouseCode(code);
            List<String> wmsOrderList = issueHeaders.stream().map(ProFeedback::getWmsOrderNo).collect(Collectors.toList());
            adjustQueryRequest.setCodeList(wmsOrderList);
            log.info("查询状态请求参数：{}", JSON.toJSONString(adjustQueryRequest));
            try {
                Result<List<AdjustQueryResponse>> listResult = iAdjustMesQuery.list(adjustQueryRequest);
                log.info("查询状态返回结果：{}", JSON.toJSONString(listResult));
                if (listResult.checkSuccess()){
                    List<AdjustQueryResponse> data = listResult.getData();
                    data.forEach(adjustQueryResponse -> orderStatusMap.put(adjustQueryResponse.getCode(), adjustQueryResponse.getStatusDesc()));
                }
            }catch (Exception e){
                log.error("查询状态异常 error={}",e.getMessage(), e);
            }
        });
        List<ProFeedbackListResVo> resVoList = list.stream().map(issueHeader -> {
            ProFeedbackListResVo proFeedbackListResVo = ConvertUtil.beanConvert(issueHeader, ProFeedbackListResVo.class);
            if (orderStatusMap.containsKey(proFeedbackListResVo.getWmsOrderNo())) {
                proFeedbackListResVo.setWmsOrderStatus(orderStatusMap.get(proFeedbackListResVo.getWmsOrderNo()));
            }
            return proFeedbackListResVo;
        }).collect(Collectors.toList());
        dataTable.setRows(resVoList);
        return dataTable;
    }

    /**
     * 导出生产报工记录列表
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:feedback:export')")
    @Log(title = "生产报工记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProFeedback proFeedback) {
        List<ProFeedback> list = proFeedbackService.selectProFeedbackList(proFeedback);
        ExcelUtil<ProFeedback> util = new ExcelUtil<ProFeedback>(ProFeedback.class);
        util.exportExcel(response, list, "生产报工记录数据");
    }

    /**
     * 获取生产报工记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:feedback:query')")
    @GetMapping(value = "/{recordId}")
    public AjaxResult getInfo(@PathVariable("recordId") Long recordId) {
        ProFeedback proFeedback = proFeedbackService.selectProFeedbackByRecordId(recordId);
        ProFeedbackDetailInfoResVo proFeedbackDetailInfoResVo = new ProFeedbackDetailInfoResVo();
        proFeedbackDetailInfoResVo.setProFeedbackResVo(ConvertUtil.beanConvert(proFeedback, ProFeedbackResVo.class));
        if (Objects.nonNull(proFeedback.getWmsBatchDetail())){
            List<ProFeedbackWmsBatchDetailResVo> detailResVos = JSON.parseArray(proFeedback.getWmsBatchDetail(), ProFeedbackWmsBatchDetailResVo.class);
            detailResVos.forEach(detailResVo -> {
                detailResVo.setInventoryTypeDesc(InventoryTypeEnum.getEnum(detailResVo.getInventoryType()).getMessage());
                detailResVo.setSkuQualityDesc(SkuQualityEnum.getEnum(detailResVo.getSkuQuality()).getMessage());
            });
            proFeedbackDetailInfoResVo.setWmsBatchDetailList(detailResVos);
        }
        if (Objects.nonNull(proFeedback.getIssueLossDetail())){
            List<ProFeedbackIssueLossDetailResVo> detailResVos = JSON.parseArray(proFeedback.getIssueLossDetail(), ProFeedbackIssueLossDetailResVo.class);
            proFeedbackDetailInfoResVo.setIssueLossDetailList(detailResVos);
        }
        return AjaxResult.success(proFeedbackDetailInfoResVo);
    }

    private void calculateQuantity(ProFeedbackAddReqVo proFeedbackAddReqVo, ProFeedback proFeedback) {
        BigDecimal uncheck = proFeedbackAddReqVo.getQuantityUncheck() == null ? BigDecimal.ZERO : proFeedbackAddReqVo.getQuantityUncheck();
//        BigDecimal unqualified = proFeedbackAddReqVo.getQuantityUnquanlified() == null ? BigDecimal.ZERO : proFeedbackAddReqVo.getQuantityUnquanlified();
        BigDecimal leaveSample = proFeedbackAddReqVo.getLeaveSampleQuantity() == null ? BigDecimal.ZERO : proFeedbackAddReqVo.getLeaveSampleQuantity();
        BigDecimal inspection = proFeedbackAddReqVo.getInspectionQuantity() == null ? BigDecimal.ZERO : proFeedbackAddReqVo.getInspectionQuantity();
        // 报工数量=待检验＋留样＋送检
        BigDecimal totalNum = uncheck.add(leaveSample).add(inspection);
        proFeedback.setQuantityFeedback(totalNum);
//        BigDecimal lossQuantity = proFeedbackAddReqVo.getLossQuantity() == null ? BigDecimal.ZERO : proFeedbackAddReqVo.getLossQuantity();
//        proFeedback.setLossQuantity(lossQuantity);
    }

    /**
     * 新增生产报工记录
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:feedback:add')")
    @Log(title = "生产报工记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProFeedbackAddReqVo proFeedbackAddReqVo) {
        log.info("新增生产报工记录请求参数：{}", JSON.toJSONString(proFeedbackAddReqVo));
        ProFeedback proFeedback = ConvertUtil.beanConvert(proFeedbackAddReqVo, ProFeedback.class);
        Long taskId = proFeedbackAddReqVo.getTaskId();
        ProTask proTask = proTaskService.selectProTaskByTaskId(taskId);
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(proFeedbackAddReqVo.getWorkorderId());
        if (Objects.isNull(proWorkorder)){
            log.info("工单为空");
            return AjaxResult.error("工单为空");
        }
        if (Objects.equals(proWorkorder.getStatus(), OrderStatusEnum.FINISHED.getCode())){
            log.info("工单已完成");
            return AjaxResult.error("工单已完成");
        }
        proFeedback.setProcessId(proTask.getProcessId());
        proFeedback.setRouteId(proTask.getRouteId());
        // 检查工作站和工艺工序配置
        AjaxResult result = checkRouteAndProcess(proFeedback);
        if (result != null) {
            return result;
        }
        result = checkProTask(proFeedback,proTask);
        if (result != null) {
            return result;
        }
        this.calculateQuantity(proFeedbackAddReqVo, proFeedback);
        // 检查数量
        result = checkQuantity(proFeedback,proTask);
        if (result != null) {
            return result;
        }
        if (Objects.nonNull(proFeedbackAddReqVo.getLossDetail())) {
            proFeedback.setIssueLossDetail(JSON.toJSONString(proFeedbackAddReqVo.getLossDetail()));
        }
        proFeedback.setWorkorderCode(proWorkorder.getWorkorderCode());


        String feedbackCode = autoCodeUtil.genSerialCode(UserConstants.FEEDBACK_CODE, "");
        proFeedback.setFeedbackCode(feedbackCode);
        proFeedback.setCreateBy(getUsername());
        proFeedback.setStatus(UserConstants.ORDER_STATUS_PREPARE);
        log.info("新增生产报工记录参数：{}", JSON.toJSONString(proFeedback));
        int i = proFeedbackService.insertProFeedback(proFeedback);
        return toAjax(i);
    }

    /**
     * 修改生产报工记录
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:feedback:edit')")
    @Log(title = "生产报工记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProFeedbackEditReqVo proFeedbackEditReqVo) {
        log.info("修改生产报工记录请求参数：{}", JSON.toJSONString(proFeedbackEditReqVo));
        ProFeedback proFeedback = ConvertUtil.beanConvert(proFeedbackEditReqVo, ProFeedback.class);
        if (Objects.isNull(proFeedbackEditReqVo.getRecordId())){
            return AjaxResult.error("报工记录ID不能为空");
        }
        ProTask proTask = proTaskService.selectProTaskByTaskId(proFeedback.getTaskId());
        // 检查工作站和工艺工序配置
        AjaxResult result = checkProTask(proFeedback,proTask);
        if (result != null) {
            return result;
        }
        result = checkQuantity(proFeedback, proTask);
        if (result != null) {
            return result;
        }

        if (Objects.nonNull(proFeedbackEditReqVo.getLossDetail())) {
            proFeedback.setIssueLossDetail(JSON.toJSONString(proFeedbackEditReqVo.getLossDetail()));
        }
        //更新为待审核
//        proFeedback.setStatus(UserConstants.ORDER_STATUS_APPROVING);
        log.info("修改生产报工记录参数：{}", JSON.toJSONString(proFeedback));
        int i = proFeedbackService.updateProFeedback(proFeedback);
        return toAjax(i);
    }

    /**
     * 检查对应生产任务状态
     *
     * @param proFeedback
     * @return
     */
    private AjaxResult checkProTask(ProFeedback proFeedback,ProTask proTask) {
//        ProTask proTask = proTaskService.selectProTaskByTaskId(proFeedback.getTaskId());
        if (Objects.isNull(proTask)){
            return AjaxResult.error("当前生产任务不存在，无法报工");
        }
        if (Objects.equals(proTask.getStatus(), UserConstants.ORDER_STATUS_FINISHED)) {
            return AjaxResult.error("当前生产任务已完成，无法报工");
        }
        proFeedback.setWarehouseCode(proTask.getWarehouseCode());
        proFeedback.setWarehouseName(proTask.getWarehouseName());
        proFeedback.setOwnerCode(proTask.getOwnerCode());
        proFeedback.setOwnerName(proTask.getOwnerName());
        proFeedback.setTaskId(proTask.getTaskId());
        proFeedback.setTaskCode(proTask.getTaskCode());
        proFeedback.setItemCode(proTask.getItemCode());
        proFeedback.setItemName(proTask.getItemName());
        proFeedback.setWorkstationId(proTask.getWorkstationId());
        proFeedback.setWorkstationName(proTask.getWorkstationName());
        proFeedback.setWorkstationCode(proFeedback.getWorkstationCode());
        return null;
    }

    /**
     * 检查对应工艺工序
     *
     * @param proFeedback
     * @return
     */
    private AjaxResult checkRouteAndProcess(ProFeedback proFeedback) {
        //检查对应的工艺路线和工序配置
        if (StringUtils.isNotNull(proFeedback.getRouteId()) && StringUtils.isNotNull(proFeedback.getProcessId())) {
            ProRouteProcess param = new ProRouteProcess();
            param.setRouteId(proFeedback.getRouteId());
            param.setProcessId(proFeedback.getProcessId());
            List<ProRouteProcess> processes = proRouteProcessService.selectProRouteProcessList(param);
            if (CollectionUtil.isEmpty(processes)) {
                return AjaxResult.error("未找到生产任务对应的工艺工序配置！");
            }
        } else {
            return AjaxResult.error("当前生产任务对应的工艺工序配置无效！");
        }
        return null;
    }

    // 辅助方法：将工作站信息赋值给报工记录
    private void setWorkstationInfo(ProFeedback proFeedback, MdWorkstation workstation) {
        proFeedback.setProcessId(workstation.getProcessId());
        proFeedback.setProcessCode(workstation.getProcessCode());
        proFeedback.setProcessName(workstation.getProcessName());
        proFeedback.setWorkorderName(workstation.getWorkstationCode());
        proFeedback.setWorkorderCode(workstation.getWorkstationCode());
    }

    /**
     * 检查工作站和工艺工序配置
     */
    private AjaxResult checkWorkstationAndProcess(ProFeedback proFeedback) {
        MdWorkstation workstation = mdWorkstationService.selectMdWorkstationByWorkstationId(proFeedback.getWorkstationId());
        if (StringUtils.isNotNull(workstation)) {
            setWorkstationInfo(proFeedback, workstation);
        } else {
            return AjaxResult.error("当前生产任务对应的工作站不存在！");
        }
        return null;
    }

    /**
     * 检查数量
     *
     * @param proFeedback
     * @param proTask
     * @return
     */
    private AjaxResult checkQuantity(ProFeedback proFeedback, ProTask proTask) {
        if (Objects.isNull(proFeedback.getQuantityUncheck())) {
            return AjaxResult.error("请输入待检测数量!");
        }
//        if (Objects.isNull(proFeedback.getQuantityUnquanlified())) {
//            return AjaxResult.error("请输入次品数量！");
//        }
        if (Objects.isNull(proFeedback.getLossQuantity())) {
            return AjaxResult.error("请输入损耗数量！");
        }
        if (Objects.isNull(proFeedback.getLeaveSampleQuantity())) {
            return AjaxResult.error("请输入留样&送检数量！");
        }
        if (proFeedback.getQuantityFeedback().compareTo(BigDecimal.ZERO) <= 0){
            return AjaxResult.error("报工数量必须大于0！");
        }
        BigDecimal historyFeedbackQuantity = BigDecimal.ZERO;
        List<ProFeedback> feedbackList = proFeedbackService.selectProFeedbackByTaskId(proFeedback.getTaskId());
        if (!CollectionUtils.isEmpty(feedbackList)){
            historyFeedbackQuantity = feedbackList.stream()
                    .filter(f -> Objects.equals(f.getStatus(), OrderStatusEnum.FINISHED.getCode()))
                    .map(ProFeedback::getQuantityFeedback)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        log.info("历史报工数量:{} 此次报工数量:{} 任务需求数量:{}", historyFeedbackQuantity, proFeedback.getQuantityFeedback(), proTask.getQuantity());
        if (proFeedback.getQuantityFeedback().add(historyFeedbackQuantity).compareTo(proTask.getQuantity()) > 0){
            return AjaxResult.error("本次报工数量:"+proFeedback.getQuantityFeedback()+"加历史报工数量:"+historyFeedbackQuantity+"大于排产数量:"+proTask.getQuantity()+"，请调整！");
        }
        return null;
    }


    /**
     * 删除生产报工记录
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:feedback:remove')")
    @Log(title = "生产报工记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{recordIds}")
    public AjaxResult remove(@PathVariable Long[] recordIds) {
        return toAjax(proFeedbackService.deleteProFeedbackByRecordIds(recordIds));
    }

    /**
     * 生产报工提交审核
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:feedback:approve')")
    @Log(title = "生产报工审核", businessType = BusinessType.UPDATE)
    @PostMapping("/approve")
    public AjaxResult approve(@RequestBody ProFeedbackApproveReqVo proFeedbackApproveReqVo) {
        try {
            proFeedbackService.approveProFeedback(proFeedbackApproveReqVo);
            return AjaxResult.success("审核通过");
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     *
     * 废弃方法 已经不使用了
     * 执行报工
     * 1.更新生产任务和生产工单的进度
     * 2.物料消耗
     * 3.产品产出
     *
     * @param recordId
     * @return
     */
    @Deprecated
    @PreAuthorize("@ss.hasPermi('mes:pro:feedback:edit')")
    @Log(title = "生产报工执行", businessType = BusinessType.UPDATE)
    @Transactional
    @PutMapping("/{recordId}")
    public AjaxResult execute(@PathVariable("recordId") Long recordId) {

        if (!StringUtils.isNotNull(recordId)) {
            return AjaxResult.error("请先保存单据");
        }

        ProFeedback feedback = proFeedbackService.selectProFeedbackByRecordId(recordId);
        if (Objects.isNull(feedback)){
            return AjaxResult.error("当前报工记录不存在！");
        }
        if (feedback.getQuantityFeedback().compareTo(BigDecimal.ZERO) < 0) {
            return AjaxResult.error("报工数量必须大于0");
        }

        ProWorkorder workorder = proWorkorderService.selectProWorkorderByWorkorderId(feedback.getWorkorderId());

        ProTask task = proTaskService.selectProTaskByTaskId(feedback.getTaskId());

        //判断当前生产任务的状态，如果已经完成则不能再报工
        if (UserConstants.ORDER_STATUS_FINISHED.equals(task.getStatus())) {
            return AjaxResult.error("当前生产工单的状态为已完成，不能继续报工，请刷新生产任务列表！");
        }

        //仍旧有待检数量时不能执行
        if (StringUtils.isNotNull(feedback.getQuantityUncheck()) && feedback.getQuantityUncheck().compareTo(BigDecimal.ZERO) > 0) {
            return AjaxResult.error("当前报工单未完成检验（待检数量大于0），无法执行报工！");
        }

        //更新生产任务的生产数量
        BigDecimal quantityProduced, quantityQuanlify, quantityUnquanlify;
        quantityQuanlify = task.getQuantityQuanlify() == null ? new BigDecimal(0) : task.getQuantityQuanlify();
        quantityUnquanlify = task.getQuantityUnquanlify() == null ? new BigDecimal(0) : task.getQuantityUnquanlify();
        quantityProduced = task.getQuantityProduced() == null ? new BigDecimal(0) : task.getQuantityProduced();
        task.setQuantityProduced(quantityProduced.add(feedback.getQuantityFeedback()));
        task.setQuantityQuanlify(quantityQuanlify.add(feedback.getQuantityQualified()));
        task.setQuantityUnquanlify(quantityUnquanlify.add(feedback.getQuantityUnquanlified()));

        proTaskService.updateProTask(task);

        //如果是关键工序，则更新当前工单的已生产数量，进行产品产出动作
        if (proRouteProcessService.checkKeyProcess(feedback)) {
            //更新生产工单的生产数量
            BigDecimal produced = workorder.getQuantityProduced() == null ? new BigDecimal(0) : workorder.getQuantityProduced();
            BigDecimal feedBackQuantity = feedback.getQuantityFeedback() == null ? new BigDecimal(0) : feedback.getQuantityFeedback();
            workorder.setQuantityProduced(produced.add(feedBackQuantity));
            proWorkorderService.updateProWorkorder(workorder);

            //生成产品产出记录单
            WmProductProduce productRecord = wmProductProduceService.generateProductProduce(feedback);
            //执行产品产出入线边库
            executeProductProduce(productRecord);
        }

        //根据当前工序的物料BOM配置，进行物料消耗
        //先生成消耗单
        WmItemConsume itemConsume = wmItemConsumeService.generateItemConsume(feedback);
        if (StringUtils.isNotNull(itemConsume)) {
            //再执行库存消耗动作
            executeItemConsume(itemConsume);
        }

        //更新报工单的状态
        feedback.setStatus(UserConstants.ORDER_STATUS_FINISHED);
        proFeedbackService.updateProFeedback(feedback);
        return AjaxResult.success();
    }

    /**
     * 执行产品产出入线边库动作
     *
     * @param record
     */
    private void executeProductProduce(WmProductProduce record) {
        List<ProductProductTxBean> beans = wmProductProduceService.getTxBeans(record.getRecordId());
        storageCoreService.processProductProduce(beans);
        record.setStatus(UserConstants.ORDER_STATUS_FINISHED);
        wmProductProduceService.updateWmProductProduce(record);
    }

    /**
     * 执行物料消耗库存动作
     *
     * @param record
     */
    private void executeItemConsume(WmItemConsume record) {
        //需要在此处进行分批次领料的线边库扣减
        List<ItemConsumeTxBean> beans = wmItemConsumeService.getTxBeans(record.getRecordId());
        storageCoreService.processItemConsume(beans);
        record.setStatus(UserConstants.ORDER_STATUS_FINISHED);
        wmItemConsumeService.updateWmItemConsume(record);
    }

}
