package com.ktg.mes.pro.domain.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 原材料损耗统计
 * @date 2025/6/7 10:28
 */
@Data
public class ProFeedbackIssueLossDetailAddResVo implements Serializable {
    @ApiModelProperty(value = "原材料代码")
    private String itemCode;
    @ApiModelProperty(value = "原材料名称")
    private String itemName;
    @ApiModelProperty(value = "损耗原因")
    private String lossReason;
    @ApiModelProperty(value = "损耗数量")
    private BigDecimal quantity;
    @ApiModelProperty(value = "备注")
    private String remark;
}
