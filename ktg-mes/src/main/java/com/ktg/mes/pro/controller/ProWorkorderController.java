package com.ktg.mes.pro.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.goods.facade.IGoodsManagementRpcFacade;
import com.danding.business.client.rpc.goods.result.GoodsManagementRpcResult;
import com.danding.business.client.rpc.goods.result.MesGoodsRpcResult;
import com.ktg.common.annotation.Log;
import com.ktg.common.constant.UserConstants;
import com.ktg.common.core.controller.BaseController;
import com.ktg.common.core.domain.AjaxResult;
import com.ktg.common.core.domain.entity.SysDictData;
import com.ktg.common.core.page.TableDataInfo;
import com.ktg.common.enums.BusinessType;
import com.ktg.common.enums.ProductRequirementEnum;
import com.ktg.common.enums.WorkorderPushStatusEnum;
import com.ktg.common.enums.YesNoEnum;
import com.ktg.common.utils.ConvertUtil;
import com.ktg.common.utils.poi.ExcelUtil;
import com.ktg.mes.md.domain.MdProductBom;
import com.ktg.mes.md.domain.MdUnitMeasure;
import com.ktg.mes.md.domain.MdWorkshop;
import com.ktg.mes.md.service.IMdProductBomService;
import com.ktg.mes.md.service.IMdUnitMeasureService;
import com.ktg.mes.md.service.IMdWorkshopService;
import com.ktg.mes.md.service.MesGoodsService;
import com.ktg.mes.pro.domain.ProTask;
import com.ktg.mes.pro.domain.ProWorkorder;
import com.ktg.mes.pro.domain.ProWorkorderBom;
import com.ktg.mes.pro.domain.dto.ProWorkOrderErpAttachDTO;
import com.ktg.mes.pro.domain.req.*;
import com.ktg.mes.pro.domain.res.*;
import com.ktg.mes.pro.service.IProFeedbackService;
import com.ktg.mes.pro.service.IProTaskService;
import com.ktg.mes.pro.service.IProWorkorderBomService;
import com.ktg.mes.pro.service.IProWorkorderService;
import com.ktg.mes.wm.utils.WmBarCodeUtil;
import com.ktg.system.service.ISysDictDataService;
import com.ktg.system.strategy.AutoCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生产工单Controller
 *
 * <AUTHOR>
 * @menu 生产工单
 * @date 2022-05-09
 */
@Slf4j
@RestController
@RequestMapping("/mes/pro/workorder")
public class ProWorkorderController extends BaseController {
    @Autowired
    private IProWorkorderService proWorkorderService;

    @Autowired
    private IProWorkorderBomService proWorkorderBomService;

    @Autowired
    private IMdProductBomService mdProductBomService;

    @Autowired
    private IProTaskService proTaskService;
    @Autowired
    private IProFeedbackService proFeedbackService;

    @Autowired
    private WmBarCodeUtil wmBarCodeUtil;

    @Autowired
    private AutoCodeUtil autoCodeUtil;

    @Autowired
    private ISysDictDataService sysDictDataService;
//
//    @DubboReference
//    private IMesGoodsRpcFacade iMesGoodsRpcFacade;

    @Autowired
    private MesGoodsService mesGoodsService;
    @Autowired
    private IMdWorkshopService mdWorkshopService;
    @Autowired
    private IMdUnitMeasureService mdUnitMeasureService;

    @DubboReference
    private IGoodsManagementRpcFacade goodsManagementRpcFacade;

    /**
     * 查询生产工单列表
     */
    @GetMapping("/list")
    public TableDataInfo<ProWorkorderDetailInfo> list(ProWorkorderListReqVo reqVo) {
        startPage();
        if (Objects.nonNull(reqVo.getMultipleStatus())) {
            reqVo.setStatusList(Arrays.asList(reqVo.getMultipleStatus().split(",")));
        }
        List<ProWorkorder> list = proWorkorderService.proWorkorderPage(reqVo);
        Map<Long, List<ProTask>> workorderTaskMap;
//        Map<Long, List<ProFeedback>> proFeedbackMap;
        if (!CollectionUtils.isEmpty(list)) {
            List<Long> workorderIdList = list.stream().map(ProWorkorder::getWorkorderId).distinct().collect(Collectors.toList());
            List<ProTask> tasks = proTaskService.selectProTaskListByWorkorderId(workorderIdList);
            workorderTaskMap = tasks.stream().collect(Collectors.groupingBy(ProTask::getWorkorderId));
//            List<ProFeedback> proFeedbackList = proFeedbackService.selectProFeedbackByWorkorderId(workorderIdList);
//            proFeedbackMap = proFeedbackList.stream().collect(Collectors.groupingBy(ProFeedback::getWorkorderId));
        } else {
            workorderTaskMap = new HashMap<>();
//            proFeedbackMap = new HashMap<>();
        }
        List<SysDictData> dictDataList = sysDictDataService.selectByType("workorder_requirement");
        Map<String, String> dictDataMap = dictDataList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        List<ProWorkorderDetailInfo> detailInfos = list.stream().map(workorder -> {
            ProWorkorderDetailInfo detailInfo = ConvertUtil.beanConvert(workorder, ProWorkorderDetailInfo.class);
            if (Objects.nonNull(detailInfo.getUnitOfMeasure())) {
                MdUnitMeasure mdUnitMeasure = mdUnitMeasureService.selectMdUnitByCode(detailInfo.getUnitOfMeasure());
                if (Objects.nonNull(mdUnitMeasure)) {
                    detailInfo.setUnitOfMeasureDesc(mdUnitMeasure.getMeasureName());
                }
            }
            if (workorderTaskMap.containsKey(workorder.getWorkorderId())) {
                List<ProTask> proTaskList = workorderTaskMap.get(workorder.getWorkorderId());
                if (!CollectionUtils.isEmpty(proTaskList)) {
                    detailInfo.setTaskCount(proTaskList.size());
                    BigDecimal totalProduced = proTaskList.stream().map(ProTask::getQuantityProduced).reduce(BigDecimal.ZERO, BigDecimal::add);
                    detailInfo.setQuantityProduced(totalProduced);
                } else {
                    detailInfo.setQuantityProduced(BigDecimal.ZERO);
                    detailInfo.setTaskCount(0);
                }
            }
            if (Objects.nonNull(workorder.getRequirement())) {
                String requirement = workorder.getRequirement();
                List<String> requirementList = JSON.parseArray(requirement, String.class);
                String requirementLabel = requirementList
                        .stream()
                        .map(r -> dictDataMap.getOrDefault(r, null))
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(","));
                detailInfo.setRequirementDesc(requirementLabel);
            }
            if (Objects.nonNull(workorder.getPushStatus())) {
                YesNoEnum yesNoEnum = YesNoEnum.getByCode(workorder.getPushStatus());
                if (Objects.nonNull(yesNoEnum)) {
                    detailInfo.setPushStatusDesc(yesNoEnum.getInfo());
                }
            }
            if (Objects.nonNull(workorder.getOrderSource())) {
                if (Objects.equals(workorder.getOrderSource(), "ERP")) {
                    detailInfo.setOrderSourceDesc("客户");
                } else if (Objects.equals(workorder.getOrderSource(), "SELF")) {
                    detailInfo.setOrderSourceDesc("自建");
                }
            }
//            if (proFeedbackMap.containsKey(workorder.getWorkorderId())){
//                List<ProFeedback> proFeedbackList = proFeedbackMap.get(workorder.getWorkorderId());
//                if (!CollectionUtils.isEmpty(proFeedbackList)){
//                    BigDecimal quantityFeedbackTotal = proFeedbackList.stream().map(ProFeedback::getQuantityFeedback).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    detailInfo.setQuantityProduced(quantityFeedbackTotal);
//                }else{
//                    detailInfo.setQuantityProduced(BigDecimal.ZERO);
//                }
//            }
            if (StringUtils.isNotEmpty(workorder.getPushErpAttach())) {
                ProWorkOrderErpAttachDTO attachDTO = JSON.parseObject(workorder.getPushErpAttach(), ProWorkOrderErpAttachDTO.class);
                detailInfo.setDeclareOrderAttachUrl(attachDTO.getDeclareOrderAttachUrl());
                detailInfo.setDeclareOrderAttachName(attachDTO.getDeclareOrderAttachName());
                detailInfo.setChecklistAttachUrl(attachDTO.getChecklistAttachUrl());
                detailInfo.setChecklistAttachName(attachDTO.getChecklistAttachName());
            }
            return detailInfo;
        }).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(detailInfos);
        return dataTable;
    }

    /**
     * 任务列表
     *
     * @param proWorkorder
     * @return
     */
    @GetMapping("/listWithTaskJson")
    public TableDataInfo listWithTaskJson(ProWorkorder proWorkorder) {
        startPage();
        List<ProWorkorder> list = proWorkorderService.selectProWorkorderList(proWorkorder);
        Iterator<ProWorkorder> iterator = list.iterator();
        while (iterator.hasNext()) {
            ProWorkorder workorder = iterator.next();
            List<ProTask> tasks = proTaskService.selectProTaskProcessViewByWorkorder(workorder.getWorkorderId());
            workorder.setTasks(tasks);
        }
        return getDataTable(list);
    }

    /**
     * 导出生产工单列表
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:workorder:export')")
    @Log(title = "生产工单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProWorkorder proWorkorder) {
        List<ProWorkorder> list = proWorkorderService.selectProWorkorderList(proWorkorder);
        ExcelUtil<ProWorkorder> util = new ExcelUtil<ProWorkorder>(ProWorkorder.class);
        util.exportExcel(response, list, "生产工单数据");
    }

    /**
     * 获取生产工单详细信息
     */
    @GetMapping(value = "/{workorderId}")
    public AjaxResult getInfo(@PathVariable("workorderId") Long workorderId) {
        log.info("获取生产工单详细信息 workorderId={}", workorderId);
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
        ProWorkorderDetailInfo detailInfo = ConvertUtil.beanConvert(proWorkorder, ProWorkorderDetailInfo.class);
        if (StringUtils.isNotEmpty(proWorkorder.getPushErpAttach())) {
            ProWorkOrderErpAttachDTO attachDTO = JSON.parseObject(proWorkorder.getPushErpAttach(), ProWorkOrderErpAttachDTO.class);
            detailInfo.setDeclareOrderAttachUrl(attachDTO.getDeclareOrderAttachUrl());
            detailInfo.setDeclareOrderAttachName(attachDTO.getDeclareOrderAttachName());
            detailInfo.setChecklistAttachUrl(attachDTO.getChecklistAttachUrl());
            detailInfo.setChecklistAttachName(attachDTO.getChecklistAttachName());
        }
        if (Objects.nonNull(proWorkorder.getKeyItems())) {
            List<ProWorkOrderKeyMdItemResVo> mdItemResVos = JSON.parseArray(proWorkorder.getKeyItems(), ProWorkOrderKeyMdItemResVo.class);
            for (ProWorkOrderKeyMdItemResVo mdItemResVo : mdItemResVos) {
                if (Objects.nonNull(mdItemResVo.getProduceRequirement())) {
                    ProductRequirementEnum requirementEnum = ProductRequirementEnum.getByCode(mdItemResVo.getProduceRequirement());
                    if (Objects.nonNull(requirementEnum)) {
                        mdItemResVo.setProduceRequirementDesc(requirementEnum.getDesc());
                    }
                }
            }
            detailInfo.setKeyItems(mdItemResVos);
        }
        List<SysDictData> dictDataList = sysDictDataService.selectByType("workorder_requirement");
        Map<String, String> dictDataMap = dictDataList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        if (Objects.nonNull(proWorkorder.getRequirement())) {
            String requirement = proWorkorder.getRequirement();
            List<String> requirementList = JSON.parseArray(requirement, String.class);
            String requirementLabel = requirementList
                    .stream()
                    .map(r -> dictDataMap.getOrDefault(r, null))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(","));
            detailInfo.setRequirementDesc(requirementLabel);
        }
        log.info("获取生产工单详细信息 detailInfo={}", JSON.toJSONString(detailInfo));
        return AjaxResult.success(detailInfo);
    }

    /**
     * 新增生产工单
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:workorder:add')")
    @Log(title = "生产工单", businessType = BusinessType.INSERT)
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add(@RequestBody ProWorkOrderAddReqVo reqVo) {
        synchronized (this) {
            log.info("新增生产工单 reqVo={}", JSON.toJSONString(reqVo));
            if (Objects.isNull(reqVo.getWorkshopId()) || Objects.isNull(reqVo.getWorkshopName())) {
                return AjaxResult.error("请选择车间");
            }

            ProWorkorder proWorkorder = ConvertUtil.beanConvert(reqVo, ProWorkorder.class);
            //生成编码
            String proWorkOrderCode = autoCodeUtil.genSerialCode(UserConstants.PRO_WORK_ORDER_CODE);
            proWorkorder.setWorkorderCode(proWorkOrderCode);
            //不知道干啥的 先留着
            if (proWorkorder.getParentId() == null || proWorkorder.getParentId() == 0) {
                proWorkorder.setAncestors("0");
            }
            MesGoodsRpcResult goodsDetail = mesGoodsService.getGoodsDetail(reqVo.getOwnerCode(), reqVo.getProductCode());
            log.info("新增生产工单 获取商品详情 goodsDetail={}", JSON.toJSONString(goodsDetail));
            if (Objects.isNull(goodsDetail)) {
                return AjaxResult.error("新增生产工单失败:商品档案不存在" + reqVo.getProductCode() + "，请检查!");
            }
            if (StringUtils.isEmpty(goodsDetail.getUnit())) {
                return AjaxResult.error("新增生产工单失败:无法获取成品单位" + reqVo.getProductCode() + "，请检查!");
            }
            proWorkorder.setUnitOfMeasure(goodsDetail.getUnit());
            StringBuilder skuWrap = new StringBuilder();
            skuWrap.append(goodsDetail.getProdSpec()).append(goodsDetail.getUnitName());
            if (StringUtils.isNotEmpty(goodsDetail.getProdSpecUnitName())) {
                skuWrap.append("/").append(goodsDetail.getProdSpecUnitName());
            }
            proWorkorder.setSkuWrap(skuWrap.toString());


            //调用erp获取保质期
            log.info("新增生产工单 查询商品档案信息 sku={} ownerCode={}", reqVo.getProductCode(), reqVo.getOwnerCode());
            List<GoodsManagementRpcResult> goodsManagementRpcResults = goodsManagementRpcFacade.queryRpcGoodsBy(reqVo.getProductCode(), reqVo.getOwnerCode());
            log.info("新增生产工单 获取商品档案信息结果 goodsManagementRpcResults={}", JSON.toJSONString(goodsManagementRpcResults));
            if (CollectionUtils.isEmpty(goodsManagementRpcResults)) {
                log.error("新增生产工单失败 商品档案信息不存在 sku={}", reqVo.getProductCode());
                return AjaxResult.error("新增生产工单失败:商品档案信息不存在 sku=" + reqVo.getProductCode());
            }
            GoodsManagementRpcResult goodsManagementRpcResult = goodsManagementRpcResults.get(0);
            Integer shelfLife = goodsManagementRpcResult.getShelfLife();
            if (Objects.isNull(shelfLife)) {
                log.error("新增生产工单失败:商品效期为空 sku={}", reqVo.getProductCode());
                return AjaxResult.error("创新增生产工单失败:商品效期为空 sku=" + reqVo.getProductCode());
            }
            log.info("新增生产工单 获取商品BOM信息 sku={} ownerCode={}", reqVo.getProductCode(), reqVo.getOwnerCode());
            List<MesGoodsRpcResult.MaterialDetail> materialDetailList = mesGoodsService.getMaterialDetail(reqVo.getOwnerCode(), reqVo.getProductCode());
            log.info("新增生产工单 获取商品BOM信息结果 materialDetailList={}", JSON.toJSONString(materialDetailList));
            for (MesGoodsRpcResult.MaterialDetail bom : materialDetailList) {
                if (StringUtils.isEmpty(bom.getUnit())) {
                    log.error("根据生产工单中的产品生成BOM物料行产品BOM单位为空 bom={}", JSON.toJSONString(bom));
                    return AjaxResult.error("无法获取原材料:" + bom.getSku() + "的单位,请检查");
                }
            }
            Date productionDate = reqVo.getProductionDate();
            Date expiryDate = calculateExpiryDate(productionDate, shelfLife);
            proWorkorder.setExpirationDate(expiryDate);
            proWorkorder.setShelfLife(Long.valueOf(shelfLife));

            if (Objects.nonNull(reqVo.getKeyItems())) {
                proWorkorder.setKeyItems(JSON.toJSONString(reqVo.getKeyItems()));
            }
            proWorkorder.setOrderSource("SELF");
            proWorkorder.setPushStatus(YesNoEnum.NO.getCode());
            log.info("新增生产工单 proWorkorder={}", JSON.toJSONString(proWorkorder));
            proWorkorderService.insertProWorkorder(proWorkorder);

            Long workorderId = proWorkorder.getWorkorderId();
            generateBomLine(workorderId, materialDetailList);
            proWorkorder.setCreateBy(getUsername());
            //条码生成
            wmBarCodeUtil.generateBarCode(UserConstants.BARCODE_TYPE_WORKORDER, proWorkorder.getWorkorderId(), proWorkorder.getWorkorderCode(), proWorkorder.getWorkorderName());
            return AjaxResult.success(workorderId);

        }
    }

    public static Date calculateExpiryDate(Date productionDate, Integer shelfLife) {
        log.info("计算失效日期 productionDate={} shelfLife={}", productionDate, shelfLife);
        // 创建Calendar实例
        Calendar calendar = Calendar.getInstance();
        // 设置为生产日
        calendar.setTime(productionDate);
        // 增加保质期天数
        calendar.add(Calendar.DAY_OF_YEAR, shelfLife);
        // 返回失效日期
        log.info("计算失效日期 result={}", calendar.getTime());
        return calendar.getTime();
    }

    /**
     * 修改生产工单
     */
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("@ss.hasPermi('mes:pro:workorder:edit')")
    @Log(title = "生产工单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProWorkOrderUpdateReqVo reqVo) {
        try {
            log.info("修改生产工单 reqVo={}", JSON.toJSONString(reqVo));
            if (Objects.isNull(reqVo)) {
                return AjaxResult.error("参数为空！");
            }
            if (Objects.isNull(reqVo.getProductionDate())) {
                return AjaxResult.error("生产日期不能为空！");
            }
            MdWorkshop mdWorkshop = mdWorkshopService.selectMdWorkshopByWorkshopId(reqVo.getWorkshopId());
            if (Objects.isNull(mdWorkshop)) {
                return AjaxResult.error("修改生产工单失败:车间信息不存在 workshopId=" + reqVo.getWorkshopId());
            }
            ProWorkorder proWorkorder = ConvertUtil.beanConvert(reqVo, ProWorkorder.class);
            proWorkorder.setWorkshopName(mdWorkshop.getWorkshopName());
            MesGoodsRpcResult goodsDetail = mesGoodsService.getGoodsDetail(reqVo.getOwnerCode(), reqVo.getProductCode());
            if (Objects.isNull(goodsDetail)) {
                return AjaxResult.error("修改生产工单失败:商品档案信息不存在 sku=" + reqVo.getProductCode());
            }
            if (CollectionUtils.isEmpty(goodsDetail.getMaterialDetailList())) {
                return AjaxResult.error("修改生产工单失败:商品BOM信息不能为空！");
            }
            if (StringUtils.isEmpty(goodsDetail.getUnit())) {
                return AjaxResult.error("修改生产工单失败:无法获取成品单位" + reqVo.getProductCode() + "，请检查!");
            }
            log.info("编辑工单 获取商品BOM信息 sku={} ownerCode={}", reqVo.getProductCode(), reqVo.getOwnerCode());
            List<MesGoodsRpcResult.MaterialDetail> materialDetailList = mesGoodsService.getMaterialDetail(reqVo.getOwnerCode(), reqVo.getProductCode());
            log.info("编辑工单 获取商品BOM信息结果 materialDetailList={}", JSON.toJSONString(materialDetailList));
            for (MesGoodsRpcResult.MaterialDetail bom : materialDetailList) {
                if (StringUtils.isEmpty(bom.getUnit())) {
                    log.error("根据生产工单中的产品生成BOM物料行产品BOM单位为空 bom={}", JSON.toJSONString(bom));
                    return AjaxResult.error("无法获取原材料:" + bom.getSku() + "的单位,请检查");
                }
            }

            proWorkorder.setUnitOfMeasure(goodsDetail.getUnit());

            StringBuilder skuWrap = new StringBuilder();
            skuWrap.append(goodsDetail.getProdSpec()).append(goodsDetail.getUnitName());
            if (StringUtils.isNotEmpty(goodsDetail.getProdSpecUnitName())) {
                skuWrap.append("/").append(goodsDetail.getProdSpecUnitName());
            }
            proWorkorder.setSkuWrap(skuWrap.toString());


            if (Objects.isNull(proWorkorder.getProductId())) {
                proWorkorder.setProductId(goodsDetail.getId());
            }
            log.info("queryRpcGoods sku={} ownerCode={}", reqVo.getProductCode(), reqVo.getOwnerCode());
            List<GoodsManagementRpcResult> goodsManagementRpcResults = goodsManagementRpcFacade.queryRpcGoodsBy(reqVo.getProductCode(), reqVo.getOwnerCode());
            log.info("queryRpcGoods result={}", JSON.toJSONString(goodsManagementRpcResults));
            if (CollectionUtils.isEmpty(goodsManagementRpcResults)) {
                log.error("修改生产工单失败:商品档案信息不存在 sku={}", reqVo.getProductCode());
                return AjaxResult.error("修改生产工单失败:商品档案信息不存在 sku=" + reqVo.getProductCode());
            }
            GoodsManagementRpcResult goodsManagementRpcResult = goodsManagementRpcResults.get(0);
            Integer shelfLife = goodsManagementRpcResult.getShelfLife();
            if (Objects.isNull(shelfLife)) {
                log.error("ERP创建生产工单失败:商品效期为空 sku={}", reqVo.getProductCode());
                return AjaxResult.error("创建生产工单失败:商品效期为空 sku=" + reqVo.getProductCode());
            }
            Date productionDate = reqVo.getProductionDate();
            Date expiryDate = calculateExpiryDate(productionDate, shelfLife);
            proWorkorder.setExpirationDate(expiryDate);


            if (Objects.nonNull(reqVo.getKeyItems())) {
                proWorkorder.setKeyItems(JSON.toJSONString(reqVo.getKeyItems()));
            }
            ProWorkorder oldWorkOrder = proWorkorderService.selectProWorkorderByWorkorderId(proWorkorder.getWorkorderId());
            int ret = proWorkorderService.updateProWorkorder(proWorkorder);
            //如果是产品和数量发生变化则需要重新生成BOM组成
            if (ret > 0) {
                if (Objects.isNull(oldWorkOrder.getProductId())) {
                    generateBomLine(proWorkorder.getWorkorderId(), materialDetailList);
                } else if (oldWorkOrder.getProductId().longValue() != proWorkorder.getProductId().longValue() ||
                        oldWorkOrder.getQuantity().compareTo(proWorkorder.getQuantity()) != 0) {
                    removeBomLine(proWorkorder.getWorkorderId());
                    generateBomLine(proWorkorder.getWorkorderId(), materialDetailList);
                }
            }
            return toAjax(ret);
        } catch (Exception e) {
            log.error("修改生产工单失败 error={}", e.getMessage(), e);
            return AjaxResult.error("修改生产工单失败," + e.getMessage());
        }
    }

    /**
     * 检查物料和关键原材料的关系
     *
     * @param mdItemId
     * @param keyMdItems
     * @return
     */
    private boolean checkProductBomItemRelation(Long mdItemId, List<ProWorkOrderKeyMdItemReqVo> keyMdItems) {
//        if (!CollectionUtils.isEmpty(keyMdItems)) {
//            List<Long> bomItemIdList = keyMdItems.stream().map(ProWorkOrderKeyMdItemReqVo::getMdItemId).distinct().collect(Collectors.toList());
//            try {
//                mdProductBomService.checkProductBomItemRelation(mdItemId, bomItemIdList);
//            } catch (Exception e) {
//                log.error("物料不在产品BOM关系中", e);
//                return false;
//            }
//        }
        return true;
    }

    /**
     * 删除生产工单
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:workorder:remove')")
    @Log(title = "生产工单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{workorderIds}")
    public AjaxResult remove(@PathVariable Long[] workorderIds) {
        log.info("删除生产工单 ids={}", JSON.toJSONString(workorderIds));
        for (Long id : workorderIds) {
            ProWorkorder workorder = proWorkorderService.selectProWorkorderByWorkorderId(id);
            if (!UserConstants.ORDER_STATUS_PREPARE.equals(workorder.getStatus())) {
                return AjaxResult.error("只能删除草稿状态单据！");
            }
            removeBomLine(id);
        }
        return toAjax(proWorkorderService.deleteProWorkorderByWorkorderIds(workorderIds));
    }

    /**
     * 生产总览
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:workorder:overview')")
    @GetMapping("overView/{workorderId}")
    public AjaxResult overView(@PathVariable("workorderId") Long workorderId) {
        log.info("生产总览 id={}", workorderId);
        try {
            ProWorkorderOverViewResVo resVo = proWorkorderService.overView(workorderId);
            return AjaxResult.success(resVo);
        } catch (Exception e) {
            log.error("生产总览 error={}", e.getMessage(), e);
            return AjaxResult.error("获取生产总览异常");
        }
    }

    /**
     * 根据生产工单中的产品生成BOM物料行
     *
     * @param workorderId
     * @param materialDetailList
     */
    private void generateBomLine(Long workorderId, List<MesGoodsRpcResult.MaterialDetail> materialDetailList) {
        log.info("根据生产工单中的产品生成BOM物料行 workorderId={}", workorderId);
        if (Objects.isNull(workorderId)) {
            return;
        }
        //先根据ID找到对应的产品
        ProWorkorder workorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
        if (Objects.isNull(workorder)) {
            log.info("根据生产工单中的产品生成BOM物料行 workorderId={} 不存在", workorderId);
            return;
        }

//        List<MesGoodsRpcResult.MaterialDetail> materialDetailList = mesGoodsService.getMaterialDetail(workorder.getOwnerCode(), workorder.getProductCode());
//        log.info("根据生产工单中的产品生成BOM物料行 workorderId={} 获取产品BOM数据 materialDetailList={}", workorderId, JSON.toJSONString(materialDetailList));
        //生成BOM数据
        BigDecimal orderQuantitiy = workorder.getQuantity();
        ProWorkorderBom workorderBom = new ProWorkorderBom();
        for (MesGoodsRpcResult.MaterialDetail bom : materialDetailList) {
            workorderBom.setWorkorderId(workorderId);
            workorderBom.setItemId(bom.getId());
            workorderBom.setItemCode(bom.getSku());
            workorderBom.setItemName(bom.getGoodsName());
            // 规格型号，erp 获取
            if (Objects.nonNull(bom.getProdSpec())) {
                workorderBom.setItemSpc(bom.getProdSpec().toString());
            }
//            workorderBom.setItemOrProduct(bom.getTagName());

            workorderBom.setUnitOfMeasure(bom.getUnit());
            workorderBom.setQuantity(orderQuantitiy.multiply(bom.getUseRatio()));
            workorderBom.setUseRatio(bom.getUseRatio());
            proWorkorderBomService.insertProWorkorderBom(workorderBom);
        }
    }

    /**
     * 删除当前工单下所有BOM组成
     *
     * @param workorderId
     */
    private void removeBomLine(Long workorderId) {
        log.info("删除当前工单下所有BOM组成 workorderId={}", workorderId);
        ProWorkorderBom param = new ProWorkorderBom();
        param.setWorkorderId(workorderId);
        proWorkorderBomService.deleteProWorkorderBomByWorkorderId(workorderId);
    }

    /**
     * 获取当前工单的物料需求清单
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:workorder:list')")
    @GetMapping("/listItems")
    public TableDataInfo listItems(ProWorkorder proWorkorder) {
        List<MdProductBom> result = new ArrayList<MdProductBom>();
        ProWorkorderBom param = new ProWorkorderBom();
        param.setWorkorderId(proWorkorder.getWorkorderId());
        List<ProWorkorderBom> boms = proWorkorderBomService.selectProWorkorderBomList(param);
        if (!CollectionUtils.isEmpty(boms)) {
            for (ProWorkorderBom bom : boms) {
                MdProductBom theBom = new MdProductBom();
                theBom.setBomItemId(bom.getItemId());
                result.addAll(getBoms(theBom, bom.getQuantity(), 0));

            }
        }
        return getDataTable(result);
    }

    /**
     * BOM清单详情
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:workorder:bomView')")
    @GetMapping("/bomView/{workorderId}")
    public AjaxResult listItems(@PathVariable("workorderId") Long workorderId) {
        try {
            List<ProWorkorderBomViewResVo> resVoList = proWorkorderService.bomView(workorderId);
            return AjaxResult.success(resVoList);
        } catch (Exception e) {
            log.error("获取BOM信息失败:{}", e.getMessage(), e);
            return AjaxResult.error(e.getMessage());
        }
    }


    private List<MdProductBom> getBoms(MdProductBom item, BigDecimal quantity, int count) {
        MdProductBom param = new MdProductBom();
        List<MdProductBom> results = new ArrayList<MdProductBom>();
        if (count > 20) {
            return results;
        }
        param.setItemId(item.getBomItemId());
        List<MdProductBom> boms = mdProductBomService.selectMdProductBomList(param);
        if (CollUtil.isNotEmpty(boms)) {
            //最多20层依赖
            count++;
            for (MdProductBom bomItem : boms
            ) {
                bomItem.setQuantity(quantity.multiply(bomItem.getQuantity()));
                results.addAll(getBoms(bomItem, bomItem.getQuantity(), count));
            }
        } else {
            results.add(item);
        }
        return results;
    }


    /**
     * 完成工单
     *
     * @param workorderId
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:workorder:edit')")
    @Log(title = "生产工单", businessType = BusinessType.UPDATE)
    @Transactional
    @PutMapping("/{workorderId}")
    public AjaxResult dofinish(@PathVariable Long workorderId) {
        ProWorkorder workorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
        if (Objects.isNull(workorder)) {
            return AjaxResult.error("工单不存在");
        }
        //将此工单下所有的生产任务状态设置为已完成
        ProTask param = new ProTask();
        param.setWorkorderId(workorderId);
        List<ProTask> tasks = proTaskService.selectProTaskList(param);

        if (CollectionUtils.isEmpty(tasks)) {
            return AjaxResult.error("此工单下没有生产任务");
        } else {
            ProTask notFinishTask = tasks.stream().filter(task -> !Objects.equals(task.getStatus(), UserConstants.ORDER_STATUS_FINISHED)).findAny().orElse(null);
            if (Objects.nonNull(notFinishTask)) {
                return AjaxResult.error("[生产任务:" + notFinishTask.getTaskCode() + "]未完成");
            }

            for (ProTask task : tasks) {
                task.setStatus(UserConstants.ORDER_STATUS_FINISHED);
                task.setEndTime(new Date());
                proTaskService.updateProTask(task);
            }
        }

        workorder.setStatus(UserConstants.ORDER_STATUS_FINISHED); //更新工单的状态
        workorder.setFinishDate(new Date());
        proWorkorderService.updateProWorkorder(workorder);
        return AjaxResult.success();
    }

    @GetMapping("/listProductRequirement")
    public AjaxResult listProductRequirement() {
        List<ProductRequirementDTO> collect = Arrays.stream(ProductRequirementEnum.values()).map(productRequirementEnum -> {
            ProductRequirementDTO productRequirementDTO = new ProductRequirementDTO();
            productRequirementDTO.setCode(productRequirementEnum.getCode());
            productRequirementDTO.setDesc(productRequirementEnum.getDesc());
            productRequirementDTO.setType(productRequirementEnum.getType());
            return productRequirementDTO;
        }).collect(Collectors.toList());
        return AjaxResult.success(collect);
    }

    /**
     * 推送ERP
     *
     * @param workorderId
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:workorder:pushErp')")
    @Log(title = "推送ERP", businessType = BusinessType.UPDATE)
    @Transactional
    @GetMapping("pushERP/{workorderId}")
    public AjaxResult pushERP(@PathVariable Long workorderId) {
        log.info("推送ERP reqVo={}", JSON.toJSONString(workorderId));
        if (Objects.isNull(workorderId)) {
            return AjaxResult.error("请选择要推送的工单");
        }
        try {
            ProWorkorder workorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
            if (Objects.isNull(workorder)) {
                return AjaxResult.error("工单不存在");
            }
            if (Objects.equals(workorder.getPushStatus(), WorkorderPushStatusEnum.YES.getCode())) {
                return AjaxResult.error("此工单已推送");
            }
            workorder.setPushStatus(WorkorderPushStatusEnum.YES.getCode());
            workorder.setPushDate(new Date());
            proWorkorderService.updateProWorkorder(workorder);
            return AjaxResult.success("推送成功");
        } catch (Exception e) {
            log.error("ERP推送生产工单失败 error={}", e.getMessage(), e);
            return AjaxResult.error("推送生产工单失败");
        }
    }

    /**
     * 推送erp带附件
     *
     * @param reqVo
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:workorder:pushErp')")
    @Log(title = "推送ERP(带附件)", businessType = BusinessType.UPDATE)
    @Transactional
    @PostMapping("pushERPWithAttach")
    public AjaxResult pushERPWithAttach(@RequestBody ProWorkOrderPushErpReqVo reqVo) {
        log.info("推送ERP(带附件) reqVo={}", JSON.toJSONString(reqVo));
        if (Objects.isNull(reqVo)) {
            return AjaxResult.error("请选择要推送的工单");
        }
        try {
            Long workorderId = reqVo.getWorkorderId();
            ProWorkorder workorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
            if (Objects.isNull(workorder)) {
                return AjaxResult.error("工单不存在");
            }
//            if (Objects.equals(workorder.getPushStatus(), WorkorderPushStatusEnum.YES.getCode())){
//                return AjaxResult.error("此工单已推送");
//            }
            if (Objects.isNull(reqVo.getChecklistAttachUrl()) || Objects.isNull(reqVo.getDeclareOrderAttachUrl())) {
                return AjaxResult.error("请上传报关单和核放单附件");
            }
            workorder.setPushStatus(WorkorderPushStatusEnum.YES.getCode());
            workorder.setPushDate(new Date());
            ProWorkOrderErpAttachDTO attachDTO = ConvertUtil.beanConvert(reqVo, ProWorkOrderErpAttachDTO.class);
            workorder.setPushErpAttach(JSON.toJSONString(attachDTO));
            log.info("推送ERP reqVo={}", JSON.toJSONString(workorder));
            proWorkorderService.updateProWorkorder(workorder);
            return AjaxResult.success("推送成功");
        } catch (Exception e) {
            log.error("ERP推送生产工单失败 error={}", e.getMessage(), e);
            return AjaxResult.error("推送生产工单失败");
        }
    }

    /**
     * 查看工单bomInfo
     *
     * @param workorderId
     * @return
     */
    @GetMapping("bomInfo/{workorderId}")
    public AjaxResult bomInfo(@PathVariable Long workorderId) {
        log.info("bomInfo workorderId={}", workorderId);
        if (Objects.isNull(workorderId)) {
            return AjaxResult.error("工单id不能为空");
        }
        try {
            ProWorkorder workorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
            if (Objects.isNull(workorder)) {
                return AjaxResult.error("工单不存在");
            }
            List<ProWorkorderBom> proWorkorderBoms = proWorkorderBomService.selectProWorkorderBomListByWorkorderId(workorderId);
            return AjaxResult.success(proWorkorderBoms);
        } catch (Exception e) {
            log.error("查询bomInfo失败 error={}", e.getMessage(), e);
            return AjaxResult.error("查询bomInfo失败");
        }
    }
}
