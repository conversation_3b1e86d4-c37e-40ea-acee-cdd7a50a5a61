package com.ktg.mes.pro.domain.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 物料消耗
 * @date 2025/4/21 11:35
 */
@Data
public class ProTaskItemUsageViewResVo implements Serializable {
    /**
     * 序号
     */
    private Integer number;

    /**
     * BOM物料编号
     */
    private String itemCode;

    /**
     * BOM物料名称
     */
    private String itemName;

    /**
     * 总领料
     */
    private BigDecimal itemTotalIssue;

    /**
     * 总退料
     */
    @Deprecated
    private BigDecimal itemTotalRt;

    /**
     * 系统应还料
     */
    private BigDecimal systemRequiredMaterialReturn;

    /**
     * 线边库剩余 前端输入
     */
    private BigDecimal lineSideWarehouseLeft;

    /**
     * 实际使用量 aka 生产使用
     * 排产数量*物料对应BOM的比例关系
     */
    private BigDecimal actualUsage;

    /**
     * 理论使用量
     */
    private BigDecimal theoreticalUsage;

    /**
     * 生产损耗
     * 手动填写，必填，默认为0；
     * 最大支持10位整数＋2位小数
     */
    private BigDecimal productionLoss;

    /**
     * 损耗量 取报工的总和
     */
    private BigDecimal lossQuantity;

    /**
     * 损耗明细
     */
    private String lossDetail;

    /**
     * 损耗百分比 lossQuantity/theoreticalUsage*100
     * 损耗量/理论使用量，百分比展示，保留两位小数
     */
    @Deprecated
    private BigDecimal lossPercentage;

    /**
     * 多到货/少到货
     */
    private BigDecimal excessShortQuantity;

    /**
     * 备注
     */
    private String remark;
}
