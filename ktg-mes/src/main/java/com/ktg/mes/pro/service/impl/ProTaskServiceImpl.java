package com.ktg.mes.pro.service.impl;

import com.alibaba.fastjson.JSON;
import com.dt.component.common.enums.bill.AdjustBusinessTypeEnum;
import com.dt.component.common.enums.bill.AdjustDetailReasonEnum;
import com.dt.component.common.enums.bill.AdjustReasonEnum;
import com.dt.component.common.enums.bill.AdjustTypeEnum;
import com.dt.platform.wms.rpc.client.mes.adjust.AdjustAddRequest;
import com.dt.platform.wms.rpc.client.mes.adjust.AdjustAddResponse;
import com.dt.platform.wms.rpc.client.mes.adjust.AdjustDetail;
import com.dt.platform.wms.rpc.client.mes.adjust.IAdjustMesClient;
import com.dt.platform.wms.rpc.client.mes.common.Result;
import com.dt.platform.wms.rpc.client.mes.stock.IStockLocationMesQuery;
import com.dt.platform.wms.rpc.client.mes.stock.StockLocationQueryRequest;
import com.dt.platform.wms.rpc.client.mes.stock.StockLocationQueryResponse;
import com.ktg.common.constant.UserConstants;
import com.ktg.common.core.domain.entity.SysDictData;
import com.ktg.common.enums.OrderStatusEnum;
import com.ktg.common.enums.ProTaskAdjustTypeEnum;
import com.ktg.common.utils.ConvertUtil;
import com.ktg.common.utils.DateUtils;
import com.ktg.common.utils.StringUtils;
import com.ktg.mes.md.domain.MdWorkshop;
import com.ktg.mes.md.service.IMdItemService;
import com.ktg.mes.md.service.IMdProductBomService;
import com.ktg.mes.md.service.IMdWorkshopService;
import com.ktg.mes.pro.domain.*;
import com.ktg.mes.pro.domain.req.ProTaskLeftItemSubmitReqVo;
import com.ktg.mes.pro.domain.req.ProTaskLeftSubmitReqVo;
import com.ktg.mes.pro.domain.req.ProTaskListReqVo;
import com.ktg.mes.pro.domain.req.TaskListQueryReqVo;
import com.ktg.mes.pro.domain.res.*;
import com.ktg.mes.pro.mapper.ProTaskMapper;
import com.ktg.mes.pro.service.*;
import com.ktg.mes.qc.service.IQcOqcService;
import com.ktg.mes.wm.domain.tx.IssueTxBean;
import com.ktg.mes.wm.service.IWmIssueHeaderService;
import com.ktg.mes.wm.service.IWmIssueLineService;
import com.ktg.mes.wm.service.IWmRtIssueService;
import com.ktg.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 生产任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-14
 */
@Slf4j
@Service
public class ProTaskServiceImpl implements IProTaskService {
    @Autowired
    private ProTaskMapper proTaskMapper;
    @Autowired
    private IMdProductBomService iMdProductBomService;
    @Autowired
    private IProTaskBomService proTaskBomService;
    @Autowired
    private IWmIssueHeaderService wmIssueHeaderService;
    private IWmIssueLineService wmIssueLineService;
    @Autowired
    private IWmRtIssueService iWmRtIssueService;
    @Autowired
    private IProFeedbackService proFeedbackService;
    @Autowired
    private IMdItemService mdItemService;
    @Autowired
    private IMdProductBomService mdProductBomService;
    @Autowired
    private IQcOqcService qcOqcService;
    @Autowired
    private IProWorkorderService proWorkorderService;
    @DubboReference
    private IAdjustMesClient adjustMesClient;
    @DubboReference
    private IStockLocationMesQuery iStockLocationMesQuery;
    @Autowired
    private IProWorkorderBomService proWorkorderBomService;
    @Autowired
    private IMdWorkshopService mdWorkshopService;
    @Autowired
    private ISysDictDataService sysDictDataService;

    /**
     * 查询生产任务
     *
     * @param taskId 生产任务主键
     * @return 生产任务
     */
    @Override
    public ProTask selectProTaskByTaskId(Long taskId) {
        return proTaskMapper.selectProTaskByTaskId(taskId);
    }

    @Override
    public List<ProTask> selectProTaskByTaskId(List<Long> taskIdList) {
        if (CollectionUtils.isEmpty(taskIdList)) {
            return new ArrayList<>();
        }
        return proTaskMapper.selectProTaskByTaskIdList(taskIdList);
    }

    /**
     * 查询生产任务列表
     *
     * @param proTask 生产任务
     * @return 生产任务
     */
    @Override
    public List<ProTask> selectProTaskList(ProTask proTask) {
        return proTaskMapper.selectProTaskList(proTask);
    }

    @Override
    public List<ProTask> proTaskPage(ProTaskListReqVo reqVo) {
        return proTaskMapper.proTaskPage(reqVo);
    }

    @Override
    public List<ProTask> selectProTaskListByWorkorderId(Long workorderId) {
        return selectProTaskListByWorkorderId(Arrays.asList(workorderId));
    }

    @Override
    public List<ProTask> selectProTaskListByWorkorderId(List<Long> workorderIdList) {
        return proTaskMapper.selectProTaskListByWorkorderId(workorderIdList);
    }

    /**
     * 查询某个工单的各个工序生产进度
     *
     * @param workorderId
     * @return
     */
    @Override
    public List<ProTask> selectProTaskProcessViewByWorkorder(Long workorderId) {
        return proTaskMapper.selectProTaskProcessViewByWorkorder(workorderId);
    }

    /**
     * 新增生产任务
     *
     * @param proTask 生产任务
     * @return 结果
     */
    @Override
    public int insertProTask(ProTask proTask) {
        proTask.setCreateTime(DateUtils.getNowDate());
        return proTaskMapper.insertProTask(proTask);
    }

    /**
     * 修改生产任务
     *
     * @param proTask 生产任务
     * @return 结果
     */
    @Override
    public int updateProTask(ProTask proTask) {
        proTask.setUpdateTime(DateUtils.getNowDate());
        return proTaskMapper.updateProTask(proTask);
    }

    /**
     * 批量删除生产任务
     *
     * @param taskIds 需要删除的生产任务主键
     * @return 结果
     */
    @Override
    public int deleteProTaskByTaskIds(Long[] taskIds) {
        return proTaskMapper.deleteProTaskByTaskIds(taskIds);
    }

    /**
     * 删除生产任务信息
     *
     * @param taskId 生产任务主键
     * @return 结果
     */
    @Override
    public int deleteProTaskByTaskId(Long taskId) {
        return proTaskMapper.deleteProTaskByTaskId(taskId);
    }

    @Override
    public ProTaskLeftViewResVo leftView(Long taskId) {
        ProTaskLeftViewResVo resVo = new ProTaskLeftViewResVo();
        ProTask proTask = this.selectProTaskByTaskId(taskId);
        if (Objects.isNull(proTask)) {
            return resVo;
        }
        Long workorderId = proTask.getWorkorderId();
        List<ProWorkorderBom> proWorkorderBoms = proWorkorderBomService.selectProWorkorderBomListByWorkorderId(workorderId);
        List<String> bomItemCodeList = proWorkorderBoms.stream().map(ProWorkorderBom::getItemCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bomItemCodeList)) {
            return resVo;
        }
        List<ProFeedback> proFeedbackList = proFeedbackService.selectProFeedbackByTaskId(taskId);
        //只使用已完成的报工数据
        List<ProFeedback> finishedProFeedback = proFeedbackList.stream().filter(proFeedback -> Objects.equals(proFeedback.getStatus(), OrderStatusEnum.FINISHED.getCode())).collect(Collectors.toList());
        //查询领料单
        List<IssueTxBean> txBeans = wmIssueHeaderService.getTxBeansByTaskIdAndItemCode(taskId, bomItemCodeList);
        Map<String, List<IssueTxBean>> itemIdIssueTxBeanMap = txBeans.stream().collect(Collectors.groupingBy(IssueTxBean::getItemCode));
        //查询退料单
//        List<RtIssueTxBean> rtTxBeans = iWmRtIssueService.getTxBeansByTaskIdAndItemCode(taskId, bomItemCodeList);
//        Map<String, List<RtIssueTxBean>> itemIdRtTxBeanMap = rtTxBeans.stream().collect(Collectors.groupingBy(RtIssueTxBean::getItemCode));
        //已报工数量
        BigDecimal quantityProduced = proTask.getQuantityProduced() == null ? BigDecimal.ZERO : proTask.getQuantityProduced();
        resVo.setQuantityProduced(quantityProduced);
        Map<String, List<ProFeedbackIssueLossDetailResVo>> skuLossMap = new HashMap<>();
        List<ProFeedbackResVo> proFeedbackResVoList = new ArrayList<>();
        for (ProFeedback proFeedback : finishedProFeedback) {
            ProFeedbackResVo proFeedbackResVo = ConvertUtil.beanConvert(proFeedback, ProFeedbackResVo.class);
            String issueLossDetail = proFeedback.getIssueLossDetail();
            if (StringUtils.isEmpty(issueLossDetail)) {
                proFeedbackResVo.setLossQuantity(BigDecimal.ZERO);
            } else {
                List<ProFeedbackIssueLossDetailResVo> issueLostDetailResVos = JSON.parseArray(issueLossDetail, ProFeedbackIssueLossDetailResVo.class);
                BigDecimal totalLoss = issueLostDetailResVos.stream().map(ProFeedbackIssueLossDetailResVo::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                proFeedbackResVo.setLossQuantity(totalLoss);
                Map<String, List<ProFeedbackIssueLossDetailResVo>> keyLossMap =
                        issueLostDetailResVos
                                .stream()
                                .collect(Collectors.groupingBy(ProFeedbackIssueLossDetailResVo::getItemCode));
                keyLossMap.forEach((itemCode, value) -> {
                    if (!CollectionUtils.isEmpty(value)) {
                        List<ProFeedbackIssueLossDetailResVo> resVoList = skuLossMap.get(itemCode);
                        if (CollectionUtils.isEmpty(resVoList)) {
                            resVoList = new ArrayList<>();
                        }
                        resVoList.addAll(value);
                        skuLossMap.put(itemCode, resVoList);
                    }
                });
            }
            proFeedbackResVoList.add(proFeedbackResVo);
        }

        List<SysDictData> sysDictDataList = sysDictDataService.selectByType("feedback_lossreason");
        Map<String, String> dictMap = sysDictDataList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        List<ProTaskItemUsageViewResVo> proTaskItemUsageViewResVos = new ArrayList<>();
        proWorkorderBoms.forEach(b -> {
            ProTaskItemUsageViewResVo proTaskLeftViewResVo = new ProTaskItemUsageViewResVo();
            proTaskLeftViewResVo.setItemCode(b.getItemCode());
            proTaskLeftViewResVo.setItemName(b.getItemName());
            //统计总领料
            List<IssueTxBean> issueTxBeans = itemIdIssueTxBeanMap.get(b.getItemCode());
            BigDecimal itemTotalIssue;
            if (!CollectionUtils.isEmpty(issueTxBeans)) {
                itemTotalIssue = issueTxBeans.stream().map(IssueTxBean::getTransactionQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                itemTotalIssue = BigDecimal.ZERO;
            }
            proTaskLeftViewResVo.setItemTotalIssue(itemTotalIssue);
//            //统计已还料
//            List<RtIssueTxBean> rtIssueTxBeans = itemIdRtTxBeanMap.get(b.getItemCode());
//            if (!CollectionUtils.isEmpty(rtIssueTxBeans)) {
//                BigDecimal itemTotalRt = rtIssueTxBeans.stream().map(RtIssueTxBean::getTransactionQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
//                proTaskLeftViewResVo.setItemTotalRt(itemTotalRt);
//            } else {
//                proTaskLeftViewResVo.setItemTotalRt(BigDecimal.ZERO);
//            }
//            BigDecimal theoreticalUsage ;
//            if (Objects.nonNull(proTask.getQuantityProduced())) {
//                theoreticalUsage = proTask.getQuantityProduced().multiply(b.getUseRatio()).setScale(2, RoundingMode.HALF_UP);
//            }else{
//                theoreticalUsage = BigDecimal.ZERO;
//            }
//            proTaskLeftViewResVo.setTheoreticalUsage(theoreticalUsage);
            BigDecimal actualUsage = quantityProduced.multiply(b.getUseRatio());
            proTaskLeftViewResVo.setActualUsage(actualUsage);
            //系统总领料 - 生产使用，系统计算，不可编辑 计算结果允许为负数
            BigDecimal systemRequiredMaterialReturn = itemTotalIssue.subtract(actualUsage);
            proTaskLeftViewResVo.setSystemRequiredMaterialReturn(systemRequiredMaterialReturn);

            if (skuLossMap.containsKey(b.getItemCode())) {
                List<ProFeedbackIssueLossDetailResVo> resVos = skuLossMap.get(b.getItemCode());
                log.info("skuLossMap resVos:{}", JSON.toJSONString(resVos));
                BigDecimal totalLoss = resVos.stream().map(ProFeedbackIssueLossDetailResVo::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                proTaskLeftViewResVo.setLossQuantity(totalLoss);
                //获取每个损耗原因的总和
                // 按损耗原因分组并计算每组的损耗数量总和
                Map<String, BigDecimal> lossReasonSumMap = resVos.stream()
                        .collect(Collectors.groupingBy(
                                ProFeedbackIssueLossDetailResVo::getLossReason,
                                Collectors.reducing(
                                        BigDecimal.ZERO,
                                        ProFeedbackIssueLossDetailResVo::getQuantity,
                                        BigDecimal::add
                                )
                        ));
                // 将统计结果转换为字符串格式
                String lossSummary = lossReasonSumMap.entrySet().stream()
                        .map(entry -> {
                            String reason = entry.getKey();
                            BigDecimal quantity = entry.getValue();
                            // 从字典中获取原因的描述
                            String reasonDesc = dictMap.getOrDefault(reason, reason);
                            return reasonDesc + ":" + quantity + "件";
                        })
                        .collect(Collectors.joining("|"));
                proTaskLeftViewResVo.setLossDetail(lossSummary);
            }
            proTaskItemUsageViewResVos.add(proTaskLeftViewResVo);
        });


        proTaskItemUsageViewResVos.forEach(proTaskItemUsageViewResVo -> {
            String itemCode = proTaskItemUsageViewResVo.getItemCode();
            proTaskItemUsageViewResVo.setLossQuantity(BigDecimal.ZERO);
            if (skuLossMap.containsKey(itemCode)) {
                List<ProFeedbackIssueLossDetailResVo> resVos = skuLossMap.get(itemCode);
                log.info("resVos:{}", JSON.toJSONString(resVos));

                BigDecimal totalLoss = resVos.stream().map(ProFeedbackIssueLossDetailResVo::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                proTaskItemUsageViewResVo.setLossQuantity(totalLoss);
                //获取每个损耗原因的总和
                // 按损耗原因分组并计算每组的损耗数量总和
                Map<String, BigDecimal> lossReasonSumMap = resVos.stream()
                        .collect(Collectors.groupingBy(
                                ProFeedbackIssueLossDetailResVo::getLossReason,
                                Collectors.reducing(
                                        BigDecimal.ZERO,
                                        ProFeedbackIssueLossDetailResVo::getQuantity,
                                        BigDecimal::add
                                )
                        ));
                // 将统计结果转换为字符串格式
                String lossSummary = lossReasonSumMap.entrySet().stream()
                        .map(entry -> {
                            String reason = entry.getKey();
                            BigDecimal quantity = entry.getValue();
                            // 从字典中获取原因的描述
                            String reasonDesc = dictMap.getOrDefault(reason, reason);
                            return reasonDesc + ":" + quantity + "件";
                        })
                        .collect(Collectors.joining("|"));
                proTaskItemUsageViewResVo.setLossDetail(lossSummary);
            }
        });
        resVo.setProTaskItemUsageViewResVoList(proTaskItemUsageViewResVos);
        resVo.setProFeedbackList(proFeedbackResVoList);
        return resVo;
    }

//    public static void main(String[] args) {
//        ProTaskLeftSubmitReqVo reqVo = new ProTaskLeftSubmitReqVo();
//        reqVo.setTaskId(1L);
//        List<ProTaskLeftItemSubmitReqVo> reqVoList = new ArrayList<>();
//        ProTaskLeftItemSubmitReqVo submitReqVo = new ProTaskLeftItemSubmitReqVo();
//        submitReqVo.setNumber(1);
//        submitReqVo.setItemCode("010015723120700001-8");
//        submitReqVo.setItemName("nimm2二宝棒棒糖200g-bom-1");
//        submitReqVo.setItemTotalIssue(BigDecimal.TEN);
//        submitReqVo.setItemTotalRt(BigDecimal.ONE);
//        submitReqVo.setLineSideWarehouseLeft(BigDecimal.valueOf(0));
//        submitReqVo.setActualUsage(BigDecimal.valueOf(9));
//        submitReqVo.setTheoreticalUsage(BigDecimal.TEN);
//        submitReqVo.setLossQuantity(BigDecimal.ZERO);
//        submitReqVo.setRemark("test");
//        reqVoList.add(submitReqVo);
//
//
//        ProTaskLeftItemSubmitReqVo submitReqVo1 = new ProTaskLeftItemSubmitReqVo();
//        submitReqVo1.setNumber(1);
//        submitReqVo1.setItemCode("010015723120700001-10");
//        submitReqVo1.setItemName("nimm2二宝棒棒糖200g-bom-2");
//        submitReqVo1.setItemTotalIssue(BigDecimal.TEN);
//        submitReqVo1.setItemTotalRt(BigDecimal.ONE);
//        submitReqVo1.setLineSideWarehouseLeft(BigDecimal.valueOf(0));
//        submitReqVo1.setActualUsage(BigDecimal.valueOf(9));
//        submitReqVo1.setTheoreticalUsage(BigDecimal.TEN);
//        submitReqVo1.setLossQuantity(BigDecimal.ZERO);
//        submitReqVo1.setRemark("test1");
//        reqVoList.add(submitReqVo1);
//
//        reqVo.setReqVoList(reqVoList);
//        System.out.println(JSON.toJSONString(reqVo));
//    }

    @Override
    public void leftItemSubmitProcess(ProTaskLeftSubmitReqVo proTaskLeftSubmitReqVo) throws Exception {
        Long taskId = proTaskLeftSubmitReqVo.getTaskId();
        ProTask proTask = this.selectProTaskByTaskId(taskId);
        if (Objects.isNull(proTask)) {
            throw new Exception("生产任务为空");
        }
        if (Objects.equals(proTask.getStatus(), OrderStatusEnum.FINISHED.getCode())) {
            throw new Exception("生产任务已完成");
        }
        BigDecimal requestQuantityProduced = proTaskLeftSubmitReqVo.getQuantityProduced();
        BigDecimal taskQuantityProduced = proTask.getQuantityProduced();
        if (requestQuantityProduced.compareTo(taskQuantityProduced) != 0) {
            throw new Exception("任务已报工数量发生变化,请重新发起余料清点");
        }
        List<ProTaskLeftItemSubmitReqVo> reqVoList = proTaskLeftSubmitReqVo.getReqVoList();
        if (CollectionUtils.isEmpty(reqVoList)) {
            throw new Exception("提交数据为空");
        }
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(proTask.getWorkorderId());
        if (Objects.isNull(proWorkorder)) {
            throw new Exception("工单为空");
        }
        if (Objects.equals(proWorkorder.getStatus(), OrderStatusEnum.FINISHED.getCode())) {
            throw new Exception("工单已完成");
        }
        int i = 1;
        for (ProTaskLeftItemSubmitReqVo proTaskLeftItemSubmitReqVo : reqVoList) {
            BigDecimal lineSideWarehouseLeft = proTaskLeftItemSubmitReqVo.getLineSideWarehouseLeft();
            BigDecimal lossQuantity = proTaskLeftItemSubmitReqVo.getLossQuantity();
            if (lineSideWarehouseLeft.compareTo(lossQuantity) < 0) {
                throw new Exception("第" + (i) + "条物料线边库实际剩余小于异常物料值，请检查！");
            }
            i++;
        }

        Map<String, ProTaskLeftItemSubmitReqVo> itemCodeSubmitMap = reqVoList.stream()
                .filter(r -> Objects.nonNull(r.getActualUsage()) && r.getActualUsage().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toMap(ProTaskLeftItemSubmitReqVo::getItemCode, Function.identity(), (v1, v2) -> v1));

        List<ProWorkorderBom> proWorkorderBoms = proWorkorderBomService.selectProWorkorderBomListByWorkorderId(proWorkorder.getWorkorderId());
//        List<String> bomItemCodeList = proWorkorderBoms.stream().map(ProWorkorderBom::getItemCode).distinct().collect(Collectors.toList());
        //查询领料单
        List<IssueTxBean> issueTxBeans = wmIssueHeaderService.getTxBeansByTaskId(taskId);
        if (CollectionUtils.isEmpty(issueTxBeans)) {
            throw new Exception("未查询到已完成的领料单");
        }
        Long workshopId = proTask.getWorkshopId();
        MdWorkshop mdWorkshop = mdWorkshopService.selectMdWorkshopByWorkshopId(workshopId);
        List<String> skuLotNoList = issueTxBeans.stream().map(IssueTxBean::getSkuLotNo).distinct().collect(Collectors.toList());

        List<ProTaskItemSkuSelectDTO> selectedList = new ArrayList<>();

        Map<String, String> itemCodeLossSkuLotNoMap = new HashMap<>();

        if (!CollectionUtils.isEmpty(itemCodeSubmitMap)) {
            StockLocationQueryRequest queryRequest = new StockLocationQueryRequest();
            queryRequest.setWarehouseCode(proWorkorder.getWarehouseCode());
            queryRequest.setCargoCode(proWorkorder.getOwnerCode());
            queryRequest.setSkuCodeList(proWorkorderBoms.stream().map(ProWorkorderBom::getItemCode).distinct().collect(Collectors.toList()));
            queryRequest.setSkuLotNoList(skuLotNoList);
            queryRequest.setLocationCode(mdWorkshop.getAvlLineSideLocationCode());
            log.info("查询库存信息请求参数：{}", JSON.toJSONString(queryRequest));
            List<StockLocationQueryResponse> responseList = iStockLocationMesQuery.list(queryRequest);
            log.info("库存查询结果：{}", JSON.toJSONString(responseList));
            Map<String, List<StockLocationQueryResponse>> itemCodeMap = responseList.stream()
                    .filter(r -> Objects.nonNull(r.getZoneAttr()) || StringUtils.isEmpty(r.getZoneAttr()))
                    .sorted(Comparator.comparing(StockLocationQueryResponse::getReceiveDate))
                    .collect(Collectors.groupingBy(StockLocationQueryResponse::getSkuCode));

            for (ProWorkorderBom b : proWorkorderBoms) {
                String itemCode = b.getItemCode();
                ProTaskLeftItemSubmitReqVo proTaskLeftItemSubmitReqVo = itemCodeSubmitMap.get(itemCode);
                if (Objects.isNull(proTaskLeftItemSubmitReqVo)) {
                    continue;
                }
                BigDecimal actualUsage = proTaskLeftItemSubmitReqVo.getActualUsage();
                BigDecimal productionLoss = proTaskLeftItemSubmitReqVo.getProductionLoss();
                log.info("itemCode={} actualUsage={} productionLoss={}", itemCode, actualUsage, productionLoss);
                if (itemCodeMap.containsKey(itemCode)) {
                    List<StockLocationQueryResponse> itemResponseList = itemCodeMap.get(itemCode);
                    if (!CollectionUtils.isEmpty(itemResponseList)) {
                        BigDecimal lossQuantity = proTaskLeftItemSubmitReqVo.getLossQuantity();
                        if (Objects.nonNull(lossQuantity) && lossQuantity.compareTo(BigDecimal.ZERO) > 0) {
                            //找到可有库存最多的库存批次id
                            StockLocationQueryResponse maxStockItem = itemResponseList.stream()
                                    .max(Comparator.comparing(StockLocationQueryResponse::getAvailableQty))
                                    .get();
                            log.info("sku:{} 最大库存量的批次id：{}", itemCode, maxStockItem.getSkuLotNo());
                            String skuLotNo = maxStockItem.getSkuLotNo();
                            // 使用skuLotNo进行后续操作
                            itemCodeLossSkuLotNoMap.put(itemCode, skuLotNo);
                        }
                        for (StockLocationQueryResponse r : itemResponseList) {
                            BigDecimal availableQty = r.getAvailableQty();
                            int compared = actualUsage.compareTo(availableQty);
                            if (compared <= 0) {
                                // 如果实际使用小于等于可用，直接使用该批次
                                log.info("生产使用 sku:{} 使用批次id：{} 实际使用小于等于可用，直接使用该批次", itemCode, r.getSkuLotNo());
                                ProTaskItemSkuSelectDTO selectDTO = new ProTaskItemSkuSelectDTO();
                                selectDTO.setSkuCode(r.getSkuCode());
                                selectDTO.setSkuLotNo(r.getSkuLotNo());
                                selectDTO.setAdjustQty(actualUsage);
                                selectDTO.setLocationCode(r.getLocationCode());
                                selectDTO.setExternalLinkBillNo(r.getExternalLinkBillNo());
                                selectDTO.setType(ProTaskAdjustTypeEnum.PRODUCTION_USAGE.getCode());
                                selectedList.add(selectDTO);
                                availableQty = availableQty.subtract(actualUsage);
                                log.info("itemCode:{} 批次id:{} 剩余可用库存：{}", itemCode, r.getSkuLotNo(), availableQty);
                                r.setAvailableQty(availableQty);
                                actualUsage = BigDecimal.ZERO;
                                break;
                            } else {
                                // 实际使用大于可用，则使用可用库存，并继续使用下一批次
                                ProTaskItemSkuSelectDTO selectDTO = new ProTaskItemSkuSelectDTO();
                                selectDTO.setSkuCode(r.getSkuCode());
                                selectDTO.setSkuLotNo(r.getSkuLotNo());
                                selectDTO.setAdjustQty(r.getAvailableQty());
                                selectDTO.setLocationCode(r.getLocationCode());
                                selectDTO.setExternalLinkBillNo(r.getExternalLinkBillNo());
                                selectDTO.setType(ProTaskAdjustTypeEnum.PRODUCTION_USAGE.getCode());
                                selectedList.add(selectDTO);
                                actualUsage = actualUsage.subtract(availableQty);
                                r.setAvailableQty(BigDecimal.ZERO);
                            }
                        }
                        if (actualUsage.compareTo(BigDecimal.ZERO) > 0) {
                            log.info("生产使用可用库存批次不足");
                            throw new Exception("可用库存批次不足");
                        }
                        if (productionLoss.compareTo(BigDecimal.ZERO) > 0) {
                            for (StockLocationQueryResponse r : itemResponseList) {
                                BigDecimal availableQty = r.getAvailableQty();
                                if (availableQty.compareTo(BigDecimal.ZERO) == 0) {
                                    continue;
                                }
                                int compared = productionLoss.compareTo(availableQty);
                                if (compared <= 0) {
                                    // 如果实际使用小于等于可用，直接使用该批次
                                    log.info("生产损耗 sku:{} 使用批次id：{} 实际使用小于等于可用，直接使用该批次", itemCode, r.getSkuLotNo());
                                    ProTaskItemSkuSelectDTO selectDTO = new ProTaskItemSkuSelectDTO();
                                    selectDTO.setSkuCode(r.getSkuCode());
                                    selectDTO.setSkuLotNo(r.getSkuLotNo());
                                    selectDTO.setAdjustQty(productionLoss);
                                    selectDTO.setLocationCode(r.getLocationCode());
                                    selectDTO.setExternalLinkBillNo(r.getExternalLinkBillNo());
                                    selectDTO.setType(ProTaskAdjustTypeEnum.PRODUCTION_LOSS.getCode());
                                    selectedList.add(selectDTO);
                                    productionLoss = BigDecimal.ZERO;
                                    break;
                                } else {
                                    // 实际使用大于可用，则使用可用库存，并继续使用下一批次
                                    ProTaskItemSkuSelectDTO selectDTO = new ProTaskItemSkuSelectDTO();
                                    selectDTO.setSkuCode(r.getSkuCode());
                                    selectDTO.setSkuLotNo(r.getSkuLotNo());
                                    selectDTO.setAdjustQty(r.getAvailableQty());
                                    selectDTO.setLocationCode(r.getLocationCode());
                                    selectDTO.setExternalLinkBillNo(r.getExternalLinkBillNo());
                                    selectDTO.setType(ProTaskAdjustTypeEnum.PRODUCTION_LOSS.getCode());
                                    selectedList.add(selectDTO);
                                    productionLoss = productionLoss.subtract(availableQty);
                                }
                            }
                            if (productionLoss.compareTo(BigDecimal.ZERO) > 0) {
                                log.info("生产损耗可用库存批次不足");
                                throw new Exception("可用库存批次不足");
                            }
                        } else {
                            log.info("生产损耗为0 不进行扣减");
                        }

                    } else {
                        throw new Exception("可用库存不足");
                    }
                }
            }
            log.info("已选择批次信息 selectedList={}", JSON.toJSONString(selectedList));
            Map<String, List<ProTaskItemSkuSelectDTO>> skuLotNoMap = selectedList.stream().collect(Collectors.groupingBy(ProTaskItemSkuSelectDTO::getSkuLotNo));
            // 生产任务 - 【完结】：MES手工创建，与库存调整(调减)1:1关系；对象为原材料调减
            AdjustAddRequest adjustAddRequest = new AdjustAddRequest();
            adjustAddRequest.setWarehouseCode(proWorkorder.getWarehouseCode());
            adjustAddRequest.setCargoCode(proWorkorder.getOwnerCode());
            adjustAddRequest.setReason(AdjustReasonEnum.MES.getCode());
            adjustAddRequest.setType(AdjustTypeEnum.SUBTRACT.getStatus());
            adjustAddRequest.setBusinessType(AdjustBusinessTypeEnum.MES_RAW_DEDUCT.getCode());
            adjustAddRequest.setBillNo(proTask.getTaskCode());
            adjustAddRequest.setNote(proTask.getTaskCode());

            List<AdjustDetail> adjustDetailList = new ArrayList<>();
            skuLotNoMap.keySet().forEach(s -> {
                List<ProTaskItemSkuSelectDTO> proTaskItemSkuSelectDTOS = skuLotNoMap.get(s);
                ProTaskItemSkuSelectDTO selectDTO = proTaskItemSkuSelectDTOS.get(0);
                AdjustDetail adjustDetail = new AdjustDetail();
                adjustDetail.setSkuCode(selectDTO.getSkuCode());
                adjustDetail.setSkuLotNo(selectDTO.getSkuLotNo());
                adjustDetail.setLocationCode(selectDTO.getLocationCode());
                adjustDetail.setReason(AdjustDetailReasonEnum.MES_DEDUCT.getCode());
                if (proTaskItemSkuSelectDTOS.size() > 1) {
                    BigDecimal totalAdjustQty = proTaskItemSkuSelectDTOS.stream().map(ProTaskItemSkuSelectDTO::getAdjustQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    adjustDetail.setAdjustQty(totalAdjustQty);
                    String remark = proTaskItemSkuSelectDTOS
                            .stream()
                            .map(skuSelectDTO -> {
                                ProTaskAdjustTypeEnum adjustTypeEnum = ProTaskAdjustTypeEnum.getByCode(skuSelectDTO.getType());
                                if (Objects.isNull(adjustTypeEnum)) {
                                    return null;
                                }
                                return adjustTypeEnum.getType() + ":" + skuSelectDTO.getAdjustQty();
                            }).filter(Objects::nonNull)
                            .collect(Collectors.joining(";"));
                    adjustDetail.setRemark(remark);
                } else {
                    adjustDetail.setAdjustQty(selectDTO.getAdjustQty());
                }
                adjustDetailList.add(adjustDetail);
            });

//            adjustDetailList = selectedList.stream().map(bom -> {
//                AdjustDetail adjustDetail = new AdjustDetail();
//                adjustDetail.setSkuCode(bom.getSkuCode());
//                adjustDetail.setSkuLotNo(bom.getSkuLotNo());
//                adjustDetail.setAdjustQty(bom.getAdjustQty());
//                adjustDetail.setLocationCode(bom.getLocationCode());
//                adjustDetail.setReason(AdjustDetailReasonEnum.MES_DEDUCT.getCode());
//                return adjustDetail;
//            }).collect(Collectors.toList());
            adjustAddRequest.setDetailList(adjustDetailList);
            log.info("生成库存调整单 req={}", JSON.toJSONString(adjustDetailList));
            Result<AdjustAddResponse> result = adjustMesClient.add(adjustAddRequest);
            log.info("调用wms接口返回 result={}", JSON.toJSONString(result));
            if (result.checkSuccess()) {
                proTask.setStatus(UserConstants.ORDER_STATUS_FINISHED);
                proTask.setWmsOrderNo(result.getData().getCode());
                proTask.setEndTime(new Date());
                proTaskMapper.updateProTask(proTask);
            } else {
                throw new Exception(result.getMessage());
            }
        } else {
            log.info("所有物料实际使用都是0 任务完成 taskId:{}", taskId);
            proTask.setStatus(UserConstants.ORDER_STATUS_FINISHED);
            proTask.setEndTime(new Date());
            proTaskMapper.updateProTask(proTask);
            return;
        }

        //生成任务bom
        this.generateTaskBomList(reqVoList, selectedList, itemCodeLossSkuLotNoMap, proTask);

        //生成出货检测单
        try {
            qcOqcService.buildQcOqcByProTask(proWorkorder, proTask);
        } catch (Exception e) {
            log.error("生成出货检测单失败 error={}", e.getMessage(), e);
        }
    }

    @Override
    public List<ProTaskCodeIdResVo> listProTaskByWorkorderId(Long workorderId) {
        List<ProTask> proTaskList = proTaskMapper.selectProTaskListByWorkorderId(Arrays.asList(workorderId));
        return proTaskList.stream().map(proTask -> {
            ProTaskCodeIdResVo resVo = new ProTaskCodeIdResVo();
            resVo.setTaskId(proTask.getTaskId());
            resVo.setTaskCode(proTask.getTaskCode());
            resVo.setProduceBatchCode(proTask.getProduceBatchCode());
            resVo.setItemCode(proTask.getItemCode());
            resVo.setItemName(proTask.getItemName());
            resVo.setWorkshopName(proTask.getWorkshopName());
            resVo.setQuantityProduced(proTask.getQuantityProduced());
            resVo.setEndTime(proTask.getEndTime());
            return resVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ProTaskCodeIdResVo> listNotFinishProTaskByWorkorderId(Long workorderId) {
        List<ProTask> proTaskList = proTaskMapper.selectProTaskListByWorkorderId(Arrays.asList(workorderId));
        proTaskList = proTaskList.stream().filter(proTask -> !Objects.equals(proTask.getStatus(), UserConstants.ORDER_STATUS_FINISHED)).collect(Collectors.toList());
        return proTaskList.stream().map(proTask -> {
            ProTaskCodeIdResVo resVo = new ProTaskCodeIdResVo();
            resVo.setTaskId(proTask.getTaskId());
            resVo.setTaskCode(proTask.getTaskCode());
            resVo.setProduceBatchCode(proTask.getProduceBatchCode());
            resVo.setItemCode(proTask.getItemCode());
            resVo.setItemName(proTask.getItemName());
            resVo.setWorkshopName(proTask.getWorkshopName());
            resVo.setQuantityProduced(proTask.getQuantityProduced());
            resVo.setEndTime(proTask.getEndTime());
            return resVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ProTaskCodeIdResVo> listProTaskByWorkorderIdAndStatus(TaskListQueryReqVo reqVo) {
        if (Objects.nonNull(reqVo.getStatus())) {
            if (reqVo.getStatus().contains(",")) {
                reqVo.setStatusList(Arrays.asList(reqVo.getStatus().split(",")));
                reqVo.setStatus(null);
            }
        }
        if (Objects.nonNull(reqVo.getExcludeStatus())) {
            if (reqVo.getExcludeStatus().contains(",")) {
                reqVo.setExcludeStatusList(Arrays.asList(reqVo.getExcludeStatus().split(",")));
                reqVo.setExcludeStatus(null);
            }
        }
        List<ProTask> proTaskList = proTaskMapper.selectProTaskListByWorkorderIdAndStatus(reqVo);
        return proTaskList.stream().map(proTask -> {
            ProTaskCodeIdResVo resVo = new ProTaskCodeIdResVo();
            resVo.setTaskId(proTask.getTaskId());
            resVo.setTaskCode(proTask.getTaskCode());
            resVo.setProduceBatchCode(proTask.getProduceBatchCode());
            resVo.setItemCode(proTask.getItemCode());
            resVo.setItemName(proTask.getItemName());
            resVo.setWorkshopName(proTask.getWorkshopName());
            resVo.setQuantityProduced(proTask.getQuantityProduced());
            resVo.setEndTime(proTask.getEndTime());
            return resVo;
        }).collect(Collectors.toList());
    }

    private void generateTaskBomList(List<ProTaskLeftItemSubmitReqVo> reqVoList, List<ProTaskItemSkuSelectDTO> adjustDetailList, Map<String, String> itemCodeLossSkuLotNoMap, ProTask proTask) {
        log.info("生成任务bom reqVoList={} adjustDetailList={} itemCodeLossSkuLotNoMap={}", JSON.toJSONString(reqVoList), JSON.toJSONString(adjustDetailList), JSON.toJSONString(itemCodeLossSkuLotNoMap));
        Map<String, List<ProTaskItemSkuSelectDTO>> skuAdjustDetailMap = adjustDetailList.stream().collect(Collectors.groupingBy(ProTaskItemSkuSelectDTO::getSkuCode));
        //保存实际使用
        List<ProTaskBom> proTaskBomList = reqVoList.stream().map(req -> {
            ProTaskBom proTaskBom = new ProTaskBom();
            proTaskBom.setWorkorderId(proTask.getWorkorderId());
            proTaskBom.setTaskId(proTask.getTaskId());
            proTaskBom.setItemCode(req.getItemCode());
            proTaskBom.setItemName(req.getItemName());
            proTaskBom.setActualUsage(req.getActualUsage() == null ? BigDecimal.ZERO : req.getActualUsage());
            proTaskBom.setLineSideWarehouseLeft(req.getLineSideWarehouseLeft() == null ? BigDecimal.ZERO : req.getLineSideWarehouseLeft());
            proTaskBom.setTheoreticalUsage(req.getTheoreticalUsage() == null ? BigDecimal.ZERO : req.getTheoreticalUsage());
            proTaskBom.setItemTotalIssue(req.getItemTotalIssue() == null ? BigDecimal.ZERO : req.getItemTotalIssue());
//            proTaskBom.setItemTotalRt(req.getItemTotalRt());
            proTaskBom.setProductionLoss(req.getProductionLoss() == null ? BigDecimal.ZERO : req.getProductionLoss());
            //系统总领料 - 生产使用，系统计算
            BigDecimal systemRequiredMaterialReturn = proTaskBom.getItemTotalIssue().subtract(proTaskBom.getActualUsage()).subtract(proTaskBom.getProductionLoss());
            proTaskBom.setSystemRequiredMaterialReturn(systemRequiredMaterialReturn);
            //生产使用＋线边库实际剩余＋生产损耗 - 系统总领料 计算结果允许为负数
            BigDecimal excessShortQuantity = proTaskBom.getTheoreticalUsage().add(proTaskBom.getLineSideWarehouseLeft()).add(proTaskBom.getProductionLoss()).subtract(proTaskBom.getItemTotalIssue());
            proTaskBom.setExcessShortQuantity(excessShortQuantity);
//            BigDecimal lossQuantity = req.getActualUsage().subtract(req.getTheoreticalUsage());
//            if (lossQuantity.compareTo(BigDecimal.ZERO) <= 0) {
//                lossQuantity = BigDecimal.ZERO;
//            }
            BigDecimal lossQuantity = req.getLossQuantity();
            if (Objects.isNull(lossQuantity)) {
                lossQuantity = BigDecimal.ZERO;
            }
            proTaskBom.setLossQuantity(lossQuantity);
            //损耗量/理论使用量，百分比展示，保留两位小数
//            BigDecimal lossPercent = lossQuantity.divide(req.getTheoreticalUsage(), 2, RoundingMode.HALF_UP);
//            proTaskBom.setLossPercentage(lossPercent);
            proTaskBom.setLossDetail(req.getLossDetail());
            proTaskBom.setRemark(req.getRemark());
            List<ProTaskItemSkuSelectDTO> detailList = skuAdjustDetailMap.get(req.getItemCode());
            if (!CollectionUtils.isEmpty(detailList)) {
                List<ProTaskBomAdjustDetailDTO> detailDTOList = ConvertUtil.listConvert(detailList, ProTaskBomAdjustDetailDTO.class);
                proTaskBom.setAdjustDetailList(JSON.toJSONString(detailDTOList));
            }
            String lossSkuLotNo = itemCodeLossSkuLotNoMap.get(req.getItemCode());
            if (Objects.nonNull(lossSkuLotNo)) {
                proTaskBom.setLossSkuLotNo(lossSkuLotNo);
            }
            return proTaskBom;
        }).collect(Collectors.toList());
        log.info("生成任务bom proTaskBomList={}", JSON.toJSONString(proTaskBomList));
        proTaskBomService.insertProTaskBomList(proTaskBomList);
    }
}
